import React, { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { SocialMediaPost } from '../../types';
import { format, addDays, addHours, isAfter, isBefore } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface PostSchedulerProps {
  onSchedule: (scheduledDate: Date) => void;
  onPublishNow: () => void;
  isLoading?: boolean;
  disabled?: boolean;
}

const PostScheduler: React.FC<PostSchedulerProps> = ({
  onSchedule,
  onPublishNow,
  isLoading = false,
  disabled = false
}) => {
  const [scheduleMode, setScheduleMode] = useState<'now' | 'schedule'>('now');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [quickOptions, setQuickOptions] = useState<{ label: string; date: Date }[]>([]);

  useEffect(() => {
    // Gerar opções rápidas de agendamento
    const now = new Date();
    const options = [
      { label: 'Em 1 hora', date: addHours(now, 1) },
      { label: 'Em 3 horas', date: addHours(now, 3) },
      { label: 'Amanhã 9h', date: new Date(addDays(now, 1).setHours(9, 0, 0, 0)) },
      { label: 'Amanhã 18h', date: new Date(addDays(now, 1).setHours(18, 0, 0, 0)) },
      { label: 'Segunda 9h', date: getNextWeekday(now, 1, 9) }, // Segunda-feira
      { label: 'Sexta 17h', date: getNextWeekday(now, 5, 17) }, // Sexta-feira
    ];
    setQuickOptions(options);
  }, []);

  const getNextWeekday = (date: Date, weekday: number, hour: number): Date => {
    const result = new Date(date);
    const days = (weekday + 7 - result.getDay()) % 7;
    result.setDate(result.getDate() + (days === 0 ? 7 : days));
    result.setHours(hour, 0, 0, 0);
    return result;
  };

  const handleQuickSchedule = (date: Date) => {
    setScheduleMode('schedule');
    setSelectedDate(format(date, 'yyyy-MM-dd'));
    setSelectedTime(format(date, 'HH:mm'));
  };

  const handleSchedule = () => {
    if (!selectedDate || !selectedTime) {
      alert('Selecione data e horário para agendamento');
      return;
    }

    const scheduledDate = new Date(`${selectedDate}T${selectedTime}`);
    const now = new Date();

    if (isBefore(scheduledDate, now)) {
      alert('A data de agendamento deve ser no futuro');
      return;
    }

    // Verificar se não é muito distante (máximo 1 ano)
    const oneYearFromNow = addDays(now, 365);
    if (isAfter(scheduledDate, oneYearFromNow)) {
      alert('A data de agendamento não pode ser superior a 1 ano');
      return;
    }

    onSchedule(scheduledDate);
  };

  const getMinDateTime = () => {
    const now = new Date();
    const minDate = format(now, 'yyyy-MM-dd');
    const minTime = format(addHours(now, 1), 'HH:mm'); // Mínimo 1 hora no futuro
    return { minDate, minTime };
  };

  const { minDate, minTime } = getMinDateTime();

  return (
    <Card title="Agendamento de Publicação">
      <div className="space-y-4">
        {/* Modo de Publicação */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light">
            Quando publicar?
          </label>
          <div className="flex gap-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="scheduleMode"
                value="now"
                checked={scheduleMode === 'now'}
                onChange={(e) => setScheduleMode(e.target.value as 'now' | 'schedule')}
                disabled={disabled}
                className="mr-2"
              />
              <span className="text-sm">Publicar agora</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="scheduleMode"
                value="schedule"
                checked={scheduleMode === 'schedule'}
                onChange={(e) => setScheduleMode(e.target.value as 'now' | 'schedule')}
                disabled={disabled}
                className="mr-2"
              />
              <span className="text-sm">Agendar publicação</span>
            </label>
          </div>
        </div>

        {/* Opções Rápidas */}
        {scheduleMode === 'schedule' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light">
              Opções Rápidas
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {quickOptions.map((option, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickSchedule(option.date)}
                  disabled={disabled}
                  className="text-xs"
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Seleção Manual de Data/Hora */}
        {scheduleMode === 'schedule' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-1">
                Data
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                min={minDate}
                disabled={disabled}
                className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md 
                         focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light 
                         bg-white dark:bg-neutral-dark dark:text-neutral-light"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-1">
                Horário
              </label>
              <input
                type="time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                min={selectedDate === minDate ? minTime : '00:00'}
                disabled={disabled}
                className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md 
                         focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light 
                         bg-white dark:bg-neutral-dark dark:text-neutral-light"
              />
            </div>
          </div>
        )}

        {/* Preview da Data Selecionada */}
        {scheduleMode === 'schedule' && selectedDate && selectedTime && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <div className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Agendado para:</strong>{' '}
              {format(new Date(`${selectedDate}T${selectedTime}`), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", {
                locale: ptBR
              })}
            </div>
          </div>
        )}

        {/* Dicas de Melhores Horários */}
        {scheduleMode === 'schedule' && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
            <div className="text-sm text-yellow-700 dark:text-yellow-300">
              <strong>💡 Dica:</strong> Os melhores horários para publicar são:
              <ul className="mt-1 ml-4 list-disc text-xs">
                <li>Facebook: 9h-10h e 15h-16h</li>
                <li>Instagram: 11h-13h e 17h-19h</li>
                <li>Twitter: 9h-10h e 19h-20h</li>
              </ul>
            </div>
          </div>
        )}

        {/* Botões de Ação */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-neutral-medium">
          {scheduleMode === 'now' ? (
            <Button
              variant="primary"
              onClick={onPublishNow}
              disabled={disabled}
              isLoading={isLoading}
            >
              {isLoading ? 'Publicando...' : 'Publicar Agora'}
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleSchedule}
              disabled={disabled || !selectedDate || !selectedTime}
              isLoading={isLoading}
            >
              {isLoading ? 'Agendando...' : 'Agendar Publicação'}
            </Button>
          )}
        </div>

        {/* Informações sobre Agendamento */}
        <div className="text-xs text-gray-500 dark:text-neutral-medium space-y-1">
          <div>• Posts agendados serão publicados automaticamente no horário selecionado</div>
          <div>• Você pode cancelar ou editar posts agendados a qualquer momento</div>
          <div>• Recomendamos agendar com pelo menos 1 hora de antecedência</div>
        </div>
      </div>
    </Card>
  );
};

export default PostScheduler;
