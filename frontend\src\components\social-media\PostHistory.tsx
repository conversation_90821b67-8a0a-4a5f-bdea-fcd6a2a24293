import React, { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { SocialMediaPost } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface PostHistoryProps {
  onRepost?: (post: SocialMediaPost) => void;
}

const PostHistory: React.FC<PostHistoryProps> = ({ onRepost }) => {
  const [posts, setPosts] = useState<SocialMediaPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'published' | 'scheduled' | 'failed'>('all');

  useEffect(() => {
    loadPostHistory();
  }, []);

  const loadPostHistory = async () => {
    setIsLoading(true);
    try {
      // TODO: Implementar carregamento real do Firestore
      // Por enquanto, dados mock
      const mockPosts: SocialMediaPost[] = [
        {
          id: '1',
          content: 'Reunião com a comunidade do bairro Centro para discutir melhorias na infraestrutura. Juntos, construímos uma cidade melhor! 🏗️ #Política #Comunidade',
          mediaUrls: ['https://via.placeholder.com/400x300'],
          platforms: ['Facebook', 'Instagram'],
          status: 'published',
          publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 horas atrás
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          metrics: {
            Facebook: { likes: 45, shares: 12, comments: 8, reach: 1200 },
            Instagram: { likes: 78, shares: 23, comments: 15, reach: 2100 }
          }
        },
        {
          id: '2',
          content: 'Amanhã às 14h teremos a inauguração da nova praça no bairro Esperança. Todos estão convidados! 🌳',
          mediaUrls: [],
          platforms: ['Facebook', 'Twitter'],
          status: 'scheduled',
          scheduledFor: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 horas no futuro
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '3',
          content: 'Visita às obras da nova escola municipal. O progresso está dentro do cronograma! 📚',
          mediaUrls: ['https://via.placeholder.com/400x300', 'https://via.placeholder.com/400x300'],
          platforms: ['Instagram'],
          status: 'failed',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          error: 'Falha na autenticação com Instagram'
        }
      ];

      setPosts(mockPosts);
    } catch (error) {
      console.error('Erro ao carregar histórico:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredPosts = posts.filter(post => {
    if (filter === 'all') return true;
    return post.status === filter;
  });

  const getStatusColor = (status: SocialMediaPost['status']) => {
    switch (status) {
      case 'published': return 'text-green-600 bg-green-100';
      case 'scheduled': return 'text-blue-600 bg-blue-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'draft': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: SocialMediaPost['status']) => {
    switch (status) {
      case 'published': return 'Publicado';
      case 'scheduled': return 'Agendado';
      case 'failed': return 'Falhou';
      case 'draft': return 'Rascunho';
      default: return 'Desconhecido';
    }
  };

  const formatDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true,
      locale: ptBR
    });
  };

  const getTotalMetrics = (post: SocialMediaPost) => {
    if (!post.metrics) return null;
    
    const totals = Object.values(post.metrics).reduce(
      (acc, metrics) => ({
        likes: acc.likes + metrics.likes,
        shares: acc.shares + metrics.shares,
        comments: acc.comments + metrics.comments,
        reach: acc.reach + metrics.reach
      }),
      { likes: 0, shares: 0, comments: 0, reach: 0 }
    );

    return totals;
  };

  if (isLoading) {
    return (
      <Card title="Histórico de Publicações">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="md" />
        </div>
      </Card>
    );
  }

  return (
    <Card title="Histórico de Publicações">
      <div className="space-y-4">
        {/* Filtros */}
        <div className="flex flex-wrap gap-2">
          {[
            { key: 'all', label: 'Todos' },
            { key: 'published', label: 'Publicados' },
            { key: 'scheduled', label: 'Agendados' },
            { key: 'failed', label: 'Falharam' }
          ].map(({ key, label }) => (
            <Button
              key={key}
              variant={filter === key ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setFilter(key as any)}
            >
              {label}
            </Button>
          ))}
        </div>

        {/* Lista de Posts */}
        <div className="space-y-4">
          {filteredPosts.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-neutral-medium">
              {filter === 'all' 
                ? 'Nenhuma publicação encontrada.' 
                : `Nenhuma publicação ${getStatusText(filter as any).toLowerCase()} encontrada.`
              }
            </div>
          ) : (
            filteredPosts.map(post => (
              <div
                key={post.id}
                className="border border-gray-200 dark:border-neutral-medium rounded-lg p-4 space-y-3"
              >
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(post.status)}`}>
                      {getStatusText(post.status)}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-neutral-medium">
                      {post.status === 'published' && post.publishedAt
                        ? `Publicado ${formatDate(post.publishedAt)}`
                        : post.status === 'scheduled' && post.scheduledFor
                        ? `Agendado para ${formatDate(post.scheduledFor)}`
                        : `Criado ${formatDate(post.createdAt)}`
                      }
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {post.platforms.map(platform => (
                      <span
                        key={platform}
                        className="text-xs px-2 py-1 bg-gray-100 dark:bg-neutral-medium rounded"
                      >
                        {platform}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Conteúdo */}
                <div className="text-sm text-gray-700 dark:text-neutral-light">
                  {post.content}
                </div>

                {/* Mídia */}
                {post.mediaUrls.length > 0 && (
                  <div className="flex gap-2 flex-wrap">
                    {post.mediaUrls.map((url, index) => (
                      <img
                        key={index}
                        src={url}
                        alt={`Mídia ${index + 1}`}
                        className="w-16 h-16 object-cover rounded border"
                      />
                    ))}
                  </div>
                )}

                {/* Métricas */}
                {post.status === 'published' && post.metrics && (
                  <div className="bg-gray-50 dark:bg-neutral-dark rounded p-3">
                    <div className="text-xs font-medium text-gray-600 dark:text-neutral-medium mb-2">
                      Métricas Totais
                    </div>
                    <div className="grid grid-cols-4 gap-4 text-center">
                      {(() => {
                        const totals = getTotalMetrics(post);
                        return totals ? (
                          <>
                            <div>
                              <div className="text-lg font-semibold text-blue-600">{totals.likes}</div>
                              <div className="text-xs text-gray-500">Curtidas</div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-green-600">{totals.shares}</div>
                              <div className="text-xs text-gray-500">Compartilhamentos</div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-purple-600">{totals.comments}</div>
                              <div className="text-xs text-gray-500">Comentários</div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-orange-600">{totals.reach}</div>
                              <div className="text-xs text-gray-500">Alcance</div>
                            </div>
                          </>
                        ) : null;
                      })()}
                    </div>
                  </div>
                )}

                {/* Erro */}
                {post.status === 'failed' && post.error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded p-3">
                    <div className="text-sm text-red-700 dark:text-red-300">
                      <strong>Erro:</strong> {post.error}
                    </div>
                  </div>
                )}

                {/* Ações */}
                <div className="flex justify-end gap-2 pt-2 border-t border-gray-100 dark:border-neutral-medium">
                  {post.status === 'failed' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onRepost?.(post)}
                    >
                      Tentar Novamente
                    </Button>
                  )}
                  {post.status === 'published' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onRepost?.(post)}
                    >
                      Republicar
                    </Button>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </Card>
  );
};

export default PostHistory;
