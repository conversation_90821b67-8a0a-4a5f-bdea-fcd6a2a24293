import React, { useState } from 'react';
import { ICONS } from '../constants';
import { usePlan } from '../hooks/usePlan';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import SentimentAnalysis from '../components/ai/SentimentAnalysis';
import AutoCategorization from '../components/ai/AutoCategorization';
import PredictiveAnalytics from '../components/ai/PredictiveAnalytics';
import SmartResponses from '../components/ai/SmartResponses';

type AIFeatureTab = 'overview' | 'sentiment' | 'categorization' | 'predictive' | 'responses';

const AIPage: React.FC = () => {
  const { currentPlan, canUseAIFeature, getAIFeatureUsage, upgradePlan } = usePlan();
  const [activeTab, setActiveTab] = useState<AIFeatureTab>('overview');

  // Para demonstração - alternar entre planos
  const handlePlanDemo = (planId: string) => {
    localStorage.setItem('demo_plan', planId);
    window.location.reload(); // Recarregar para aplicar o novo plano
  };

  const aiFeatures = [
    {
      id: 'text-analysis',
      name: 'Análise de Sentimento',
      description: 'Analise automaticamente o sentimento de textos, comentários e demandas',
      icon: ICONS.BRAIN,
      tab: 'sentiment' as AIFeatureTab,
      available: canUseAIFeature('text-analysis'),
      requiredPlan: 'Padrão',
      color: 'bg-blue-500'
    },
    {
      id: 'auto-categorization',
      name: 'Categorização Automática',
      description: 'Categorize demandas automaticamente por área e prioridade',
      icon: ICONS.ZAP,
      tab: 'categorization' as AIFeatureTab,
      available: canUseAIFeature('auto-categorization'),
      requiredPlan: 'Profissional',
      color: 'bg-purple-500'
    },
    {
      id: 'predictive-analytics',
      name: 'Análise Preditiva',
      description: 'Previsões baseadas em dados históricos e tendências',
      icon: ICONS.TRENDING_UP,
      tab: 'predictive' as AIFeatureTab,
      available: canUseAIFeature('predictive-analytics'),
      requiredPlan: 'Profissional',
      color: 'bg-green-500'
    },
    {
      id: 'smart-responses',
      name: 'Respostas Inteligentes',
      description: 'Sugestões automáticas de respostas para demandas',
      icon: ICONS.MESSAGE_SQUARE,
      tab: 'responses' as AIFeatureTab,
      available: canUseAIFeature('smart-responses'),
      requiredPlan: 'Profissional',
      color: 'bg-orange-500'
    }
  ];

  const availableFeatures = aiFeatures.filter(feature => feature.available);
  const unavailableFeatures = aiFeatures.filter(feature => !feature.available);

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Header */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-0 shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-8 h-8 text-blue-600">
                {ICONS.BRAIN}
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Inteligência Artificial
              </h1>
            </div>
            <p className="text-gray-700 text-lg">
              Potencialize sua gestão com recursos de IA avançados
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 bg-white/70 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <div className="w-5 h-5 text-yellow-500">
                {ICONS.CROWN}
              </div>
              <span className="text-sm font-medium text-gray-800">
                Plano {currentPlan?.name || 'Básico'}
              </span>
            </div>

            {/* Botões de demonstração */}
            <div className="flex items-center space-x-2 bg-white/70 backdrop-blur-sm rounded-full px-3 py-2 border border-white/20">
              <span className="text-xs text-gray-600 font-medium">Demo:</span>
              <button
                type="button"
                onClick={() => handlePlanDemo('basic')}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  currentPlan?.id === 'BASIC'
                    ? 'bg-gray-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:shadow-sm'
                }`}
              >
                Básico
              </button>
              <button
                type="button"
                onClick={() => handlePlanDemo('standard')}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  currentPlan?.id === 'STANDARD'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-blue-100 text-blue-600 hover:bg-blue-200 hover:shadow-sm'
                }`}
              >
                Padrão
              </button>
              <button
                type="button"
                onClick={() => handlePlanDemo('professional')}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  currentPlan?.id === 'PROFESSIONAL'
                    ? 'bg-purple-600 text-white shadow-md'
                    : 'bg-purple-100 text-purple-600 hover:bg-purple-200 hover:shadow-sm'
                }`}
              >
                Profissional
              </button>
            </div>
          </div>
        </div>
      </Card>

      {/* Funcionalidades Disponíveis */}
      {availableFeatures.length > 0 && (
        <Card className="p-6 shadow-lg border-0">
          <div className="flex items-center space-x-2 mb-6">
            <div className="w-6 h-6 text-green-600">
              {ICONS.CHECK}
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              Funcionalidades Disponíveis
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {availableFeatures.map((feature) => {
              const usage = getAIFeatureUsage(feature.id);
              return (
                <div
                  key={feature.id}
                  className="group border border-gray-200 rounded-xl p-5 hover:border-blue-300 hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
                  onClick={() => setActiveTab(feature.tab)}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`${feature.color} rounded-xl p-3 group-hover:scale-110 transition-transform duration-300`}>
                      <div className="w-6 h-6 text-white">
                        {feature.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                        {feature.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                        {feature.description}
                      </p>
                      {usage && usage.limit > 0 && (
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${feature.color} transition-all duration-500`}
                              style={{ width: `${Math.min((usage.used / usage.limit) * 100, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500 font-medium">
                            {usage.used} / {usage.limit}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="w-5 h-5 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all duration-300">
                      {ICONS.ARROW_RIGHT}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
      )}

      {/* Funcionalidades Indisponíveis */}
      {unavailableFeatures.length > 0 && (
        <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-gray-50 to-gray-100">
          <div className="flex items-center space-x-2 mb-6">
            <div className="w-6 h-6 text-amber-600">
              {ICONS.LOCK}
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              Funcionalidades Premium
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {unavailableFeatures.map((feature) => (
              <div
                key={feature.id}
                className="relative border border-gray-300 rounded-xl p-5 bg-white/50 backdrop-blur-sm"
              >
                <div className="absolute top-3 right-3">
                  <div className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-medium">
                    Premium
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="bg-gray-300 rounded-xl p-3">
                    <div className="w-6 h-6 text-gray-500">
                      {ICONS.LOCK}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {feature.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                      {feature.description}
                    </p>
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-800 font-medium">
                      Requer plano {feature.requiredPlan}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {unavailableFeatures.length > 0 && (
            <div className="mt-8 text-center">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6 mb-6">
                <div className="flex items-center justify-center space-x-2 mb-3">
                  <div className="w-8 h-8 text-blue-600">
                    {ICONS.BRAIN}
                  </div>
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Desbloqueie o Poder da IA
                  </h3>
                </div>
                <p className="text-gray-700 leading-relaxed">
                  Faça upgrade para acessar funcionalidades avançadas de inteligência artificial
                  e potencializar sua gestão política com insights preditivos e automação inteligente.
                </p>
              </div>
              <Button variant="primary" className="px-8 py-3 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="w-5 h-5 mr-2">
                  {ICONS.CROWN}
                </div>
                Ver Planos e Preços
              </Button>
            </div>
          )}
        </Card>
      )}

      {/* Estatísticas de Uso */}
      {availableFeatures.length > 0 && (
        <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
          <div className="flex items-center space-x-2 mb-6">
            <div className="w-6 h-6 text-purple-600">
              {ICONS.BAR_CHART}
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              Uso de IA Este Mês
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {availableFeatures.map((feature) => {
              const usage = getAIFeatureUsage(feature.id);
              if (!usage) return null;

              const percentage = usage.limit > 0 ? (usage.used / usage.limit) * 100 : 0;

              return (
                <div key={feature.id} className="text-center bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className={`${feature.color} rounded-xl p-4 mb-4 inline-block shadow-lg`}>
                    <div className="w-8 h-8 text-white">
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-900 text-sm mb-3">
                    {feature.name}
                  </h3>
                  <div className="text-3xl font-bold text-gray-900 mb-1">
                    {usage.used}
                  </div>
                  {usage.limit > 0 ? (
                    <>
                      <div className="text-sm text-gray-500 mb-3">
                        de {usage.limit} usos
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full ${feature.color} transition-all duration-500 ease-out`}
                          style={{ width: `${Math.min(percentage, 100)}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">
                        {percentage.toFixed(1)}% utilizado
                      </div>
                    </>
                  ) : (
                    <div className="text-sm text-green-600 font-medium bg-green-50 rounded-full px-3 py-1 inline-block">
                      ∞ Ilimitado
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </Card>
      )}
    </div>
  );



  const tabs = [
    { id: 'overview', name: 'Visão Geral', icon: ICONS.BRAIN },
    { id: 'sentiment', name: 'Análise de Sentimento', icon: ICONS.BRAIN, available: canUseAIFeature('text-analysis') },
    { id: 'categorization', name: 'Categorização', icon: ICONS.ZAP, available: canUseAIFeature('auto-categorization') },
    { id: 'predictive', name: 'Análise Preditiva', icon: ICONS.TRENDING_UP, available: canUseAIFeature('predictive-analytics') },
    { id: 'responses', name: 'Respostas Inteligentes', icon: ICONS.MESSAGE_SQUARE, available: canUseAIFeature('smart-responses') }
  ];

  return (
    <div className="space-y-6">
      {/* Navigation Tabs */}
      <div className="bg-white rounded-xl shadow-lg border-0 p-2">
        <nav className="flex space-x-2 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              type="button"
              key={tab.id}
              onClick={() => setActiveTab(tab.id as AIFeatureTab)}
              className={`py-3 px-6 rounded-lg font-medium text-sm flex items-center space-x-2 transition-all duration-300 whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg transform scale-105'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              } ${tab.available === false ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              disabled={tab.available === false}
            >
              <div className={`w-5 h-5 ${activeTab === tab.id ? 'text-white' : ''}`}>
                {tab.icon}
              </div>
              <span className="font-semibold">{tab.name}</span>
              {tab.available === false && (
                <div className="w-4 h-4 text-amber-500">
                  {ICONS.LOCK}
                </div>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'sentiment' && <SentimentAnalysis />}
      {activeTab === 'categorization' && <AutoCategorization />}
      {activeTab === 'predictive' && <PredictiveAnalytics />}
      {activeTab === 'responses' && <SmartResponses />}
    </div>
  );
};

export default AIPage;
