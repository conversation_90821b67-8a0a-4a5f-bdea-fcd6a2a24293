// Firebase app import - Revertendo para o padrão Firebase v9+ para importações modulares.
// FIX: 'initializeApp' and 'FirebaseApp' are direct exports from 'firebase/app' in v9+.
// 이전 수정: import { initializeApp, type FirebaseApp } from 'firebase/app';
// fix(firebase): Use namespace import for firebase/app to resolve initializeApp and FirebaseApp type errors
import * as FirebaseAppModule from 'firebase/app';

import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut, 
  onAuthStateChanged as firebaseOnAuthStateChanged, 
  User as FirebaseUser,
  createUserWithEmailAndPassword,
  updateProfile,
} from 'firebase/auth';
import { 
  getFirestore, 
  doc, 
  getDoc, 
  setDoc, 
  addDoc,
  collection,
  getDocs,
  query,
  where,
  deleteDoc,
  updateDoc,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { 
  getStorage, 
  ref, 
  uploadBytes, 
  getDownloadURL,
  deleteObject
} from "firebase/storage";

import { FIREBASE_CONFIG } from '../config/firebase';
import { 
  User, 
  UserRole, 
  WorkspaceSettings, 
  Demand, 
  Citizen, 
  AgendaEvent, 
  Document, 
  PoliticianProfile,
  DemandStatusReport,
  CitizenEngagementReport,
  TeamPerformanceReport,
  SocialMediaReport,
  ReportData,
  PoliticianSocialMedia,
  TeamMember,
  Service,
  BulkMessage
} from '../types';

// Initialize Firebase
// fix(firebase): Use FirebaseAppModule.FirebaseApp for the type
let firebaseApp: FirebaseAppModule.FirebaseApp;
let auth: ReturnType<typeof getAuth>;
let db: ReturnType<typeof getFirestore>;
let storage: ReturnType<typeof getStorage>;

try {
  console.log("Firebase - Tentando inicializar com config:", {
    projectId: FIREBASE_CONFIG.projectId,
    authDomain: FIREBASE_CONFIG.authDomain
  });
  // fix(firebase): Use FirebaseAppModule.initializeApp
  firebaseApp = FirebaseAppModule.initializeApp(FIREBASE_CONFIG);
  auth = getAuth(firebaseApp);
  db = getFirestore(firebaseApp);
  storage = getStorage(firebaseApp);
  console.log("Firebase - Inicializado com sucesso");
} catch (error) {
  console.error("Firebase - Erro na inicialização:", error);
  throw error;
}

const convertTimestampToISO = (timestamp: Timestamp | undefined | null): string => {
    return timestamp ? timestamp.toDate().toISOString() : new Date().toISOString();
};

// Função auxiliar robusta para converter timestamp ou string para ISO string
const convertToISOString = (timestampField: any): string => {
  if (!timestampField) {
    return new Date().toISOString();
  }

  // Se já é uma string, retorna como está (assumindo que é ISO string válida)
  if (typeof timestampField === 'string') {
    return timestampField;
  }

  // Se é um Timestamp do Firestore, converte
  if (timestampField instanceof Timestamp) {
    return timestampField.toDate().toISOString();
  }

  // Se é um objeto Date, converte
  if (timestampField instanceof Date) {
    return timestampField.toISOString();
  }

  // Se tem método toDate (Timestamp), usa ele
  if (timestampField && typeof timestampField.toDate === 'function') {
    return timestampField.toDate().toISOString();
  }

  // Fallback: tenta criar uma data a partir do valor
  try {
    return new Date(timestampField).toISOString();
  } catch {
    console.warn('Não foi possível converter timestamp:', timestampField);
    return new Date().toISOString();
  }
};

// --- Authentication Functions ---

export const signInUser = async (email: string, password: string): Promise<void> => {
  try {
    console.log('Firebase - Tentando fazer login com email:', email);
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    console.log('Firebase - Login bem-sucedido, buscando dados do usuário...');
    
    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));
    if (!userDoc.exists()) {
      console.error('Firebase - Dados do usuário não encontrados no Firestore');
      // Se o usuário não existe no Firestore, vamos criar com dados básicos
      await setDoc(doc(db, 'users', userCredential.user.uid), {
        id: userCredential.user.uid,
        email: userCredential.user.email,
        name: userCredential.user.displayName || email.split('@')[0],
        role: 'admin', // Define como admin por padrão
        createdAt: Timestamp.fromDate(new Date())
      });
      console.log('Firebase - Dados básicos do usuário criados no Firestore');
    } else {
      console.log('Firebase - Dados do usuário recuperados com sucesso');
    }
  } catch (error) {
    console.error('Firebase - Erro no login:', error);
    throw error;
  }
};

export const signUpUser = async (email: string, pass: string, fullName: string): Promise<FirebaseUser> => {
  if (!auth || !db) throw new Error("Firebase Auth or Firestore not initialized.");
  const userCredential = await createUserWithEmailAndPassword(auth, email, pass);
  const firebaseUser = userCredential.user;

  await updateProfile(firebaseUser, {
    displayName: fullName,
  });

  const userDocRef = doc(db, 'users', firebaseUser.uid);
  const newUserDoc = {
    uid: firebaseUser.uid,
    name: fullName,
    email: firebaseUser.email,
    role: 'staff', // Use string literal 'staff' em vez de UserRole.STAFF para garantir consistência
    createdAt: serverTimestamp(),
    avatarUrl: `https://picsum.photos/seed/${firebaseUser.uid}/100/100` 
  };
  await setDoc(userDocRef, newUserDoc);

  return firebaseUser;
};


export const signOutUser = async (): Promise<void> => {
  if (!auth) throw new Error("Firebase Auth not initialized.");
  await firebaseSignOut(auth);
};

export const onFirebaseAuthStateChanged = (callback: (user: User | null) => void) => {
  return firebaseOnAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      try {
        const userDocRef = doc(db, 'users', firebaseUser.uid);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const user: User = {
            id: firebaseUser.uid,
            email: firebaseUser.email || '',
            name: userData.name || firebaseUser.displayName || '',
            role: userData.role || UserRole.STAFF,
            masterId: userData.masterId || (userData.role === UserRole.STAFF ? undefined : firebaseUser.uid),
            avatarUrl: userData.avatarUrl
          };
          console.log('Firebase - Dados do usuário recuperados:', user);
          callback(user);
        } else {
          const newUser: User = {
            id: firebaseUser.uid,
            email: firebaseUser.email || '',
            name: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || '',
            role: UserRole.MASTER,
            masterId: firebaseUser.uid, // Master é seu próprio master
            avatarUrl: undefined
          };
          await setDoc(userDocRef, newUser);
          console.log('Firebase - Novos dados do usuário criados:', newUser);
          callback(newUser);
        }
      } catch (error) {
        console.error('Firebase - Erro ao buscar/criar dados do usuário:', error);
        callback(null);
      }
    } else {
      console.log('Firebase - Usuário não autenticado');
      callback(null);
    }
  });
};

// --- User Profile Functions ---

export const getUserById = async (userId: string): Promise<User | null> => {
  if (!db) throw new Error("Firestore not initialized.");
  
  try {
    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const user: User = {
        id: userId,
        email: userData.email || '',
        name: userData.name || '',
        role: userData.role || UserRole.STAFF,
        masterId: userData.masterId,
        avatarUrl: userData.avatarUrl
      };
      return user;
    }
    
    return null;
  } catch (error) {
    console.error('Erro ao buscar usuário por ID:', error);
    throw error;
  }
};

export const updateUserProfile = async (userId: string, updates: Partial<User>): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  
  try {
    console.log('Atualizando perfil do usuário:', { userId, updates });
    
    const userDocRef = doc(db, 'users', userId);
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };
    
    // Remove campos undefined
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData];
      }
    });
    
    await updateDoc(userDocRef, updateData);
    console.log('Perfil do usuário atualizado com sucesso');
    
  } catch (error) {
    console.error('Erro ao atualizar perfil do usuário:', error);
    throw error;
  }
};


// --- Workspace Settings Functions (Firestore & Storage) ---

const WORKSPACE_COLLECTION = 'workspaces';
const DEFAULT_WORKSPACE_ID = 'default_settings'; 

export const getWorkspaceSettings = async (workspaceId: string = DEFAULT_WORKSPACE_ID): Promise<WorkspaceSettings | null> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, WORKSPACE_COLLECTION, workspaceId);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      const data = docSnap.data();
      const convertTimestamp = (timestampField: any): string => {
        if (timestampField instanceof Timestamp) {
          return timestampField.toDate().toISOString();
        }
        return typeof timestampField === 'string' ? timestampField : new Date().toISOString();
      };
      return {
        ...data,
        id: docSnap.id,
        updatedAt: convertTimestamp(data.updatedAt),
      } as WorkspaceSettings;
    }
    return null;
  } catch (error) {
    console.error("Error fetching workspace settings:", error);
    throw error;
  }
};

export const saveWorkspaceSettings = async (settingsData: Partial<WorkspaceSettings>, workspaceId: string = DEFAULT_WORKSPACE_ID): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, WORKSPACE_COLLECTION, workspaceId);
    const dataToSave = {
      ...settingsData,
      updatedAt: serverTimestamp(), 
    };
    delete dataToSave.id; 

    await setDoc(docRef, dataToSave, { merge: true });
  } catch (error) {
    console.error("Error saving workspace settings:", error);
    throw error;
  }
};

// --- Politician Profile Functions (Firestore & Storage) ---
const POLITICIAN_PROFILE_COLLECTION = 'politician_profiles';
const DEFAULT_POLITICIAN_PROFILE_ID = 'main_profile';

export const getPoliticianProfile = async (): Promise<PoliticianProfile | null> => {
  if (!db || !auth.currentUser) {
    console.error("Firestore não inicializado ou usuário não autenticado:", { 
      dbInitialized: !!db, 
      userAuthenticated: !!auth.currentUser,
      userId: auth.currentUser?.uid 
    });
    throw new Error("Firestore não inicializado ou usuário não autenticado.");
  }
  
  try {
    console.log("Buscando perfil para usuário:", auth.currentUser.uid);
    
    // Primeiro tenta buscar pelo ID padrão
    const docSnap = await getDoc(doc(db, POLITICIAN_PROFILE_COLLECTION, DEFAULT_POLITICIAN_PROFILE_ID));
    
    if (docSnap.exists()) {
      const data = docSnap.data();
      
      // Verifica se o perfil pertence ao usuário atual
      if (data.userId === auth.currentUser.uid) {
        console.log("Perfil encontrado pelo ID padrão");
        return {
          ...data,
          id: docSnap.id,
          updatedAt: convertTimestampToISO(data.updatedAt as Timestamp),
          birthDate: data.birthDate ? (data.birthDate instanceof Timestamp ? data.birthDate.toDate().toISOString().substring(0,10) : data.birthDate) : undefined,
        } as PoliticianProfile;
      }
    }
    
    // Se não encontrou pelo ID padrão, busca por query
    console.log("Buscando perfil por query...");
    const querySnapshot = await getDocs(
      query(
        collection(db, POLITICIAN_PROFILE_COLLECTION),
        where('userId', '==', auth.currentUser.uid)
      )
    );

    console.log("Resultado da busca:", {
      empty: querySnapshot.empty,
      size: querySnapshot.size,
      docs: querySnapshot.docs.map(doc => ({ id: doc.id, data: doc.data() }))
    });

    if (!querySnapshot.empty) {
      const docSnap = querySnapshot.docs[0];
      const data = docSnap.data();
      return {
        ...data,
        id: docSnap.id,
        updatedAt: convertTimestampToISO(data.updatedAt as Timestamp),
        birthDate: data.birthDate ? (data.birthDate instanceof Timestamp ? data.birthDate.toDate().toISOString().substring(0,10) : data.birthDate) : undefined,
      } as PoliticianProfile;
    }
    return null;
  } catch (error) {
    console.error("Erro ao buscar perfil do político:", error);
    throw error;
  }
};

export const savePoliticianProfile = async (profileData: Partial<PoliticianProfile>): Promise<void> => {
  if (!db || !auth.currentUser) throw new Error("Firestore não inicializado ou usuário não autenticado.");
  
  try {
    // Verifica se já existe um perfil para o usuário atual
    const existingProfiles = await getDocs(
      query(
        collection(db, POLITICIAN_PROFILE_COLLECTION),
        where('userId', '==', auth.currentUser.uid)
      )
    );

    let docRef;
    if (!existingProfiles.empty) {
      // Usa o perfil existente
      docRef = doc(db, POLITICIAN_PROFILE_COLLECTION, existingProfiles.docs[0].id);
    } else {
      // Cria um novo perfil com ID padrão
      docRef = doc(db, POLITICIAN_PROFILE_COLLECTION, DEFAULT_POLITICIAN_PROFILE_ID);
    }

    const dataToSave = {
      ...profileData,
      userId: auth.currentUser.uid, // Sempre vincula ao usuário atual
      updatedAt: serverTimestamp(),
    };
    delete dataToSave.id; // Remove o ID dos dados a serem salvos
    
    if (!existingProfiles.empty) {
      // Atualiza o perfil existente
      await updateDoc(docRef, dataToSave);
    } else {
      // Cria um novo perfil
      await setDoc(docRef, {
        ...dataToSave,
        createdAt: serverTimestamp(),
      });
    }
  } catch (error) {
    console.error("Erro ao salvar perfil do político:", error);
    throw error;
  }
};


export const uploadFileToStorage = async (file: File, path: string): Promise<string> => {
  if (!storage || !auth.currentUser) {
    console.error("Upload falhou:", {
      storageInitialized: !!storage,
      userAuthenticated: !!auth.currentUser,
      storageBucket: storage?.app?.options?.storageBucket
    });
    throw new Error("Firebase Storage não inicializado ou usuário não autenticado.");
  }

  try {
    // Log inicial do upload
    console.log("Iniciando upload:", {
      path,
      fileName: file.name,
      size: file.size,
      type: file.type,
      userId: auth.currentUser.uid,
      userEmail: auth.currentUser.email
    });

    // Cria a referência do storage com o caminho completo
    const storageRef = ref(storage, path);
    console.log("Referência do storage criada:", storageRef.fullPath);

    // Define os metadados do arquivo
    const metadata = {
      contentType: file.type || 'application/octet-stream',
      cacheControl: 'public,max-age=7200',
      customMetadata: {
        uploadedBy: auth.currentUser.uid,
        uploadedAt: new Date().toISOString(),
        originalName: file.name
      }
    };
    console.log("Metadados do arquivo:", metadata);

    try {
      // Tenta o upload utilizando uma técnica diferente
      const arrayBuffer = await file.arrayBuffer();
      const bytes = new Uint8Array(arrayBuffer);
      
      console.log("Iniciando upload em bytes...");
      const uploadResult = await uploadBytes(storageRef, bytes, metadata);
      console.log("Upload em bytes concluído:", uploadResult);

      const downloadURL = await getDownloadURL(uploadResult.ref);
      console.log("URL de download obtida:", downloadURL);

      return downloadURL;
    } catch (uploadError: any) {
      console.error("Erro detalhado do upload:", {
        code: uploadError.code,
        message: uploadError.message,
        status: uploadError.status,
        name: uploadError.name
      });

      if (uploadError.code === 'storage/unauthorized') {
        throw new Error(`Acesso negado ao Storage. Verifique as permissões.`);
      } else if (uploadError.code === 'storage/canceled') {
        throw new Error("Upload cancelado. Tente novamente.");
      } else if (uploadError.code === 'storage/retry-limit-exceeded') {
        throw new Error("Upload falhou após várias tentativas. Tente novamente.");
      } else if (uploadError.code === 'storage/invalid-checksum') {
        throw new Error("Erro na validação do arquivo. Tente novamente.");
      }

      throw uploadError;
    }
  } catch (error) {
    console.error("Erro no processo de upload:", error);
    throw error;
  }
};

export const deleteFileFromStorage = async (filePath: string): Promise<void> => {
  if (!storage) throw new Error("Firebase Storage not initialized.");
    try {
        const storageRef = ref(storage, filePath);
        await deleteObject(storageRef);
    } catch (error) {
        if ((error as any).code === 'storage/object-not-found') {
            console.warn(`File not found, cannot delete: ${filePath}`);
            return;
        }
        console.error("Error deleting file from Firebase Storage:", error);
        throw error;
    }
};

// --- Demands Functions (Firestore) ---
const DEMANDS_COLLECTION = 'demands';

export const getDemands = async (): Promise<Demand[]> => {
  if (!db || !auth.currentUser) throw new Error("Não autenticado");
  
  try {
    const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
    const userData = userDoc.data();
    let masterId = userData?.role === UserRole.MASTER ? auth.currentUser.uid : userData?.masterId || auth.currentUser.uid;

    if (!masterId) {
      console.warn("MasterId não encontrado nos dados do usuário. Usando o ID do usuário atual como fallback.");
      masterId = auth.currentUser.uid;
    }

    console.log('Firebase - Buscando demandas para masterId:', masterId);

    const demandsCollection = collection(db, 'demands');
    const q = query(demandsCollection, where('masterId', '==', masterId));
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: convertToISOString(doc.data().createdAt),
      updatedAt: convertToISOString(doc.data().updatedAt),
    } as Demand));
  } catch (error) {
    console.error("Firebase - Erro ao buscar demandas:", error);
    throw error;
  }
};

export const addDemand = async (demandData: Omit<Demand, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, userId: string): Promise<Demand> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = await addDoc(collection(db, DEMANDS_COLLECTION), {
      ...demandData,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    const newDocSnap = await getDoc(docRef);
    const newData = newDocSnap.data();
    return {
        id: newDocSnap.id,
        ...newData,
        createdAt: convertTimestampToISO(newData?.createdAt as Timestamp),
        updatedAt: convertTimestampToISO(newData?.updatedAt as Timestamp),
        deadline: newData?.deadline ? (newData.deadline instanceof Timestamp ? newData.deadline.toDate().toISOString() : newData.deadline) : undefined,
    } as Demand;
  } catch (error) {
    console.error("Error adding demand:", error);
    throw error;
  }
};

export const updateDemand = async (demandId: string, demandData: Partial<Omit<Demand, 'id' | 'createdAt' | 'createdBy'>>): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, DEMANDS_COLLECTION, demandId);
    await updateDoc(docRef, {
      ...demandData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error updating demand:", error);
    throw error;
  }
};

export const deleteDemand = async (demandId: string): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, DEMANDS_COLLECTION, demandId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting demand:", error);
    throw error;
  }
};

// --- Citizens Functions (Firestore) ---
const CITIZENS_COLLECTION = 'citizens';

export const getCitizens = async (): Promise<Citizen[]> => {
  try {
    console.log("Firebase - Buscando cidadãos");
    const citizensCollection = collection(db, 'citizens');
    const snapshot = await getDocs(citizensCollection);
    
    const citizens = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        fullName: data.fullName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        birthDate: data.birthDate,
        voterRegistration: data.voterRegistration,
        neighborhood: data.neighborhood,
        workplace: data.workplace,
        profession: data.profession,
        socialMedia: data.socialMedia,
        whatsapp: data.whatsapp,
        notes: data.notes,
        tags: data.tags || [],
        createdAt: convertTimestampToISO(data.createdAt),
        updatedAt: convertTimestampToISO(data.updatedAt),
        createdBy: data.createdBy
      } as Citizen;
    });

    console.log("Firebase - Cidadãos encontrados:", citizens.length);
    return citizens;
  } catch (error) {
    console.error("Firebase - Erro ao buscar cidadãos:", error);
    throw error;
  }
};

export const addCitizen = async (citizenData: Omit<Citizen, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, userId: string): Promise<Citizen> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    // Remover campos undefined antes de salvar
    const cleanedData = { ...citizenData } as Record<string, any>;
    
    // Remover campos de primeiro nível que são undefined
    Object.keys(cleanedData).forEach(key => {
      if (cleanedData[key] === undefined) {
        delete cleanedData[key];
      }
    });
    
    // Verificar e limpar o objeto voterRegistration se existir
    if (cleanedData.voterRegistration) {
      // Se voterRegistration for um objeto, remover campos undefined dentro dele
      if (typeof cleanedData.voterRegistration === 'object') {
        const voterReg = cleanedData.voterRegistration as Record<string, any>;
        
        Object.keys(voterReg).forEach(key => {
          if (voterReg[key] === undefined) {
            delete voterReg[key];
          }
        });
        
        // Se o objeto voterRegistration ficou vazio, remova-o completamente
        if (Object.keys(voterReg).length === 0) {
          delete cleanedData.voterRegistration;
        }
      } 
      // Se voterRegistration for undefined, remova-o
      else if (cleanedData.voterRegistration === undefined) {
        delete cleanedData.voterRegistration;
      }
    }

    const docRef = await addDoc(collection(db, CITIZENS_COLLECTION), {
      ...cleanedData,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    const newDocSnap = await getDoc(docRef);
    const newData = newDocSnap.data();
    
    return {
      id: newDocSnap.id,
      ...newData,
      createdAt: convertTimestampToISO(newData?.createdAt as Timestamp),
      updatedAt: convertTimestampToISO(newData?.updatedAt as Timestamp),
    } as Citizen;
  } catch (error) {
    console.error("Error adding citizen:", error);
    throw error;
  }
};

export const updateCitizen = async (citizenId: string, citizenData: Partial<Omit<Citizen, 'id' | 'createdAt' | 'createdBy'>>): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, CITIZENS_COLLECTION, citizenId);
    await updateDoc(docRef, {
      ...citizenData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error updating citizen:", error);
    throw error;
  }
};

export const deleteCitizen = async (citizenId: string): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, CITIZENS_COLLECTION, citizenId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting citizen:", error);
    throw error;
  }
};

// --- Agenda Events Functions (Firestore) ---
const AGENDA_EVENTS_COLLECTION = 'agendaEvents';  // Alterado de 'agenda_events' para 'agendaEvents'

export const getAgendaEvents = async (): Promise<AgendaEvent[]> => {
  try {
    console.log("Firebase - Iniciando busca de eventos");
    const eventsCollection = collection(db, AGENDA_EVENTS_COLLECTION);
    const snapshot = await getDocs(eventsCollection);

    console.log("Firebase - Snapshot obtido:", snapshot.size, "documentos");

    const events = snapshot.docs.map(doc => {
      const data = doc.data();

      return {
        id: doc.id,
        title: data.title,
        description: data.description,
        start: convertToISOString(data.start),
        end: convertToISOString(data.end),
        location: data.location,
        attendees: data.attendees || [],
        isAllDay: data.isAllDay || false,
        createdBy: data.createdBy,
        createdAt: convertToISOString(data.createdAt),
        updatedAt: convertToISOString(data.updatedAt)
      };
    });

    console.log("Firebase - Eventos processados:", events.length);
    return events;
  } catch (error) {
    console.error("Firebase - Erro ao buscar eventos:", error);
    throw error;
  }
};

export const addAgendaEvent = async (eventData: Omit<AgendaEvent, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, userId: string): Promise<AgendaEvent> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = await addDoc(collection(db, AGENDA_EVENTS_COLLECTION), {
      ...eventData,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    const newDocSnap = await getDoc(docRef);
    const newData = newDocSnap.data();
    return {
      id: newDocSnap.id,
      ...newData,
      start: convertToISOString(newData?.start),
      end: convertToISOString(newData?.end),
      createdAt: convertToISOString(newData?.createdAt),
      updatedAt: convertToISOString(newData?.updatedAt),
    } as AgendaEvent;
  } catch (error) {
    console.error("Error adding agenda event:", error);
    throw error;
  }
};

export const updateAgendaEvent = async (eventId: string, eventData: Partial<Omit<AgendaEvent, 'id' | 'createdAt' | 'createdBy' | 'updatedAt'>>): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, AGENDA_EVENTS_COLLECTION, eventId);
    await updateDoc(docRef, {
      ...eventData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error updating agenda event:", error);
    throw error;
  }
};

export const deleteAgendaEvent = async (eventId: string): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, AGENDA_EVENTS_COLLECTION, eventId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting agenda event:", error);
    throw error;
  }
};

// --- Documents (Metadata) Functions (Firestore & Storage) ---
const DOCUMENTS_COLLECTION = 'documents';

export const getDocumentsMetadata = async (): Promise<Document[]> => {
  try {
    console.log("Firebase - Buscando metadados dos documentos");
    const documentsCollection = collection(db, 'documents');
    const snapshot = await getDocs(documentsCollection);
    
    const documents = snapshot.docs.map(doc => {
      const data = doc.data();
      const document: Document = {
        id: doc.id,
        name: data.name,
        type: data.type,
        url: data.url,
        storagePath: data.storagePath,
        size: data.size,
        uploadedBy: data.uploadedBy,
        createdAt: convertTimestampToISO(data.createdAt)
      };
      return document;
    });

    console.log("Firebase - Documentos encontrados:", documents.length);
    return documents;
  } catch (error) {
    console.error("Firebase - Erro ao buscar documentos:", error);
    throw error;
  }
};

export const addDocumentMetadata = async (metadata: Omit<Document, 'id' | 'createdAt'>): Promise<Document> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = await addDoc(collection(db, DOCUMENTS_COLLECTION), {
      ...metadata,
      createdAt: serverTimestamp(),
    });
    const newDocSnap = await getDoc(docRef);
    const newData = newDocSnap.data();
    return {
      id: newDocSnap.id,
      ...newData,
      createdAt: convertTimestampToISO(newData?.createdAt as Timestamp),
    } as Document;
  } catch (error) {
    console.error("Error adding document metadata:", error);
    throw error;
  }
};

export const deleteDocumentAndFile = async (documentId: string, storagePath: string): Promise<void> => {
  if (!db || !storage) throw new Error("Firestore or Storage not initialized.");
  try {
    if (storagePath) { 
        await deleteFileFromStorage(storagePath); 
    } else {
        console.warn(`No storage path provided for document ID: ${documentId}, cannot delete file from Storage.`);
    }
    const docRef = doc(db, DOCUMENTS_COLLECTION, documentId);
    await deleteDoc(docRef);

  } catch (error) {
    console.error(`Error deleting document (ID: ${documentId}) and file (Path: ${storagePath}):`, error);
    throw error; 
  }
};

// --- Reports Functions ---

export const generateDemandStatusReport = async (): Promise<DemandStatusReport[]> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const demands = await getDemands();
    const statusCounts: { [key: string]: number } = {
      'pending': 0,
      'in_progress': 0,
      'completed': 0,
      'cancelled': 0
    };

    demands.forEach(demand => {
      statusCounts[demand.status]++;
    });

    const total = demands.length;
    return Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0
    }));
  } catch (error) {
    console.error("Error generating demand status report:", error);
    throw error;
  }
};

export const generateCitizenEngagementReport = async (months: number = 6): Promise<CitizenEngagementReport[]> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const citizens = await getCitizens();
    const now = new Date();
    const reports: CitizenEngagementReport[] = [];

    for (let i = 0; i < months; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthKey = date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
      
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const newCitizensCount = citizens.filter(citizen => {
        const createdAt = new Date(citizen.createdAt);
        return createdAt >= monthStart && createdAt <= monthEnd;
      }).length;

      reports.unshift({
        month: monthKey,
        interactions: Math.floor(Math.random() * 100), // Simulado - substituir por dados reais
        newCitizens: newCitizensCount,
        activeContacts: Math.floor(Math.random() * citizens.length) // Simulado - substituir por dados reais
      });
    }

    return reports;
  } catch (error) {
    console.error("Error generating citizen engagement report:", error);
    throw error;
  }
};

export const getTeamPerformanceReport = async (): Promise<TeamPerformanceReport[]> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    console.log("Iniciando geração do relatório de desempenho da equipe");
    
    // Obter o usuário atual
    const currentUser = auth.currentUser;
    
    // Buscar todos os usuários para obter seus nomes
    const usersSnapshot = await getDocs(collection(db, 'users'));
    console.log(`${usersSnapshot.size} usuários encontrados no Firestore`);
    
    // Criar um mapa de usuários para consulta rápida
    const userMap = new Map<string, any>();
    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      userMap.set(doc.id, {
        id: doc.id,
        name: userData.name || userData.displayName || (userData.email ? userData.email.split('@')[0] : 'Usuário'),
        email: userData.email
      });
      console.log(`Usuário mapeado: ${doc.id} => ${userMap.get(doc.id).name}`);
    });
    
    // Buscar membros da equipe para obter nomes mais precisos
    try {
      const teamMembersSnapshot = await getDocs(collection(db, 'team_members'));
      console.log(`${teamMembersSnapshot.size} membros da equipe encontrados`);
      
      teamMembersSnapshot.forEach(doc => {
        const memberData = doc.data();
        if (memberData.userId) {
          userMap.set(memberData.userId, {
            id: memberData.userId,
            name: memberData.name || userMap.get(memberData.userId)?.name || 'Membro da equipe',
            email: memberData.email || userMap.get(memberData.userId)?.email
          });
          console.log(`Membro da equipe mapeado: ${memberData.userId} => ${userMap.get(memberData.userId).name}`);
        }
      });
    } catch (err) {
      console.error("Erro ao buscar membros da equipe:", err);
    }
    
    // Buscar todas as demandas
    const demands = await getDemands();
    console.log(`Obtidas ${demands.length} demandas para análise de desempenho`);
    
    // Mapear usuários que aparecem nas demandas mas não estão no userMap
    demands.forEach(demand => {
      if (demand.assignedTo && !userMap.has(demand.assignedTo)) {
        userMap.set(demand.assignedTo, {
          id: demand.assignedTo,
          name: `Usuário ${demand.assignedTo.substring(0, 6)}`,
          email: null
        });
        console.log(`Usuário de demanda mapeado: ${demand.assignedTo} => ${userMap.get(demand.assignedTo).name}`);
      }
      
      if (demand.createdBy && !userMap.has(demand.createdBy)) {
        userMap.set(demand.createdBy, {
          id: demand.createdBy,
          name: `Usuário ${demand.createdBy.substring(0, 6)}`,
          email: null
        });
        console.log(`Criador de demanda mapeado: ${demand.createdBy} => ${userMap.get(demand.createdBy).name}`);
      }
    });
    
    // Criar mapa para armazenar métricas de desempenho por usuário
    const performanceMap = new Map<string, TeamPerformanceReport & { responseTimeTotal: number }>();
    
    // Processar demandas e calcular métricas
    demands.forEach(demand => {
      const assignedTo = demand.assignedTo;
      if (!assignedTo) {
        console.log(`Demanda sem atribuição: ${demand.title} (${demand.id})`);
        return;
      }
      
      // Se o usuário não estiver no mapa de desempenho, adicioná-lo
      if (!performanceMap.has(assignedTo)) {
        // Obter informações do usuário do mapa de usuários
        const user = userMap.get(assignedTo);
        const userName = user?.name || 
                         (currentUser && assignedTo === currentUser.uid ? 
                          (currentUser.displayName || currentUser.email?.split('@')[0]) : 
                          `Usuário ${assignedTo.substring(0, 6)}`);
        
        performanceMap.set(assignedTo, {
          userId: assignedTo,
          userName: userName,
          completedDemands: 0,
          averageResponseTime: 0,
          overdueTasks: 0,
          totalDemands: 0,
          responseTimeTotal: 0 // Campo auxiliar para calcular a média
        });
        
        console.log(`Adicionado usuário ao mapa de desempenho: ${userName} (${assignedTo})`);
      }
      
      // Atualizar métricas do usuário
      const userPerformance = performanceMap.get(assignedTo)!;
      userPerformance.totalDemands++;
      
      if (demand.status === 'completed') {
        userPerformance.completedDemands++;
        
        // Calcular tempo de resposta
        if (demand.createdAt && demand.updatedAt) {
          const createdDate = new Date(demand.createdAt);
          const completedDate = new Date(demand.updatedAt);
          const responseHours = Math.round((completedDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60));
          
          // Acumular tempo total para calcular média depois
          userPerformance.responseTimeTotal = (userPerformance.responseTimeTotal || 0) + responseHours;
          userPerformance.averageResponseTime = Math.round(userPerformance.responseTimeTotal / userPerformance.completedDemands);
          
          console.log(`Demanda concluída por ${userPerformance.userName}, tempo: ${responseHours}h, média atualizada: ${userPerformance.averageResponseTime}h`);
        }
      }
      
      if (demand.deadline && new Date(demand.deadline) < new Date() && demand.status !== 'completed') {
        userPerformance.overdueTasks++;
        console.log(`Demanda atrasada para ${userPerformance.userName}: ${demand.title}`);
      }
    });
    
    // Se não houver dados reais, adicionar dados de exemplo
    if (performanceMap.size === 0) {
      console.log("Nenhum dado real encontrado, adicionando dados de exemplo");
      
      performanceMap.set('example-user-1', {
        userId: 'example-user-1',
        userName: 'Ana Silva',
        completedDemands: 12,
        averageResponseTime: 24,
        overdueTasks: 2,
        totalDemands: 15,
        responseTimeTotal: 288
      });
      
      performanceMap.set('example-user-2', {
        userId: 'example-user-2',
        userName: 'Carlos Oliveira',
        completedDemands: 8,
        averageResponseTime: 36,
        overdueTasks: 1,
        totalDemands: 10,
        responseTimeTotal: 288
      });
      
      performanceMap.set('example-user-3', {
        userId: 'example-user-3',
        userName: 'Mariana Santos',
        completedDemands: 15,
        averageResponseTime: 18,
        overdueTasks: 0,
        totalDemands: 18,
        responseTimeTotal: 270
      });
    }
    
    // Converter o mapa em array e remover o campo auxiliar responseTimeTotal
    const result = Array.from(performanceMap.values()).map(({ responseTimeTotal, ...rest }) => rest);
    
    console.log(`Relatório de desempenho gerado com ${result.length} membros`);
    result.forEach(member => {
      console.log(`- ${member.userName}: ${member.completedDemands}/${member.totalDemands} demandas, ${member.overdueTasks} atrasadas, tempo médio: ${member.averageResponseTime}h`);
    });
    
    return result;
  } catch (error) {
    console.error("Error generating team performance report:", error);
    
    // Em caso de erro, retornar dados de exemplo
    return [
      {
        userId: 'example-user-1',
        userName: 'Ana Silva (exemplo)',
        completedDemands: 12,
        averageResponseTime: 24,
        overdueTasks: 2,
        totalDemands: 15
      },
      {
        userId: 'example-user-2',
        userName: 'Carlos Oliveira (exemplo)',
        completedDemands: 8,
        averageResponseTime: 36,
        overdueTasks: 1,
        totalDemands: 10
      }
    ];
  }
};

export const generateSocialMediaReport = async (): Promise<SocialMediaReport[]> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    console.log("Gerando relatório de redes sociais...");
    // Buscar dados reais de redes sociais
    const analytics = await getSocialMediaAnalytics();
    console.log("Dados de redes sociais obtidos:", analytics);
    
    if (analytics.length === 0) {
      console.log("Nenhum dado de rede social encontrado, gerando dados simulados");
      // Se não houver dados reais, gerar dados simulados
      return [
        {
          platform: 'Facebook',
          followers: 5200,
          engagement: 3.2,
          reach: 12000,
          sentiment: { positive: 65, neutral: 25, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        },
        {
          platform: 'Instagram',
          followers: 8500,
          engagement: 4.7,
          reach: 20000,
          sentiment: { positive: 70, neutral: 20, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        },
        {
          platform: 'Twitter',
          followers: 3200,
          engagement: 2.8,
          reach: 9000,
          sentiment: { positive: 55, neutral: 30, negative: 15 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        },
        {
          platform: 'YouTube',
          followers: 1800,
          engagement: 3.5,
          reach: 7500,
          sentiment: { positive: 60, neutral: 30, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        }
      ];
    }
    
    return analytics;
  } catch (error) {
    console.error("Error generating social media report:", error);
    // Em caso de erro, retornar dados simulados
    return [
      {
        platform: 'Facebook',
        followers: 5200,
        engagement: 3.2,
        reach: 12000,
        sentiment: { positive: 65, neutral: 25, negative: 10 },
        posts: {
          total: 0,
          engagementRate: 0
        }
      },
      {
        platform: 'Instagram',
        followers: 8500,
        engagement: 4.7,
        reach: 20000,
        sentiment: { positive: 70, neutral: 20, negative: 10 },
        posts: {
          total: 0,
          engagementRate: 0
        }
      },
      {
        platform: 'Twitter',
        followers: 3200,
        engagement: 2.8,
        reach: 9000,
        sentiment: { positive: 55, neutral: 30, negative: 15 },
        posts: {
          total: 0,
          engagementRate: 0
        }
      },
      {
        platform: 'YouTube',
        followers: 1800,
        engagement: 3.5,
        reach: 7500,
        sentiment: { positive: 60, neutral: 30, negative: 10 },
        posts: {
          total: 0,
          engagementRate: 0
        }
      }
    ];
  }
};

export const generateFullReport = async (startDate?: string, endDate?: string): Promise<ReportData> => {
  if (!db) throw new Error("Firestore not initialized.");
  console.log("Gerando relatório completo...");
  console.log("Parâmetros:", { startDate, endDate });
  
  const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const end = endDate ? new Date(endDate) : new Date();
  console.log("Período do relatório:", { start, end });

  try {
    // Buscar demandas
    console.log("Buscando demandas...");
    let demands: Demand[] = [];
    try {
      demands = await getDemands();
      console.log(`${demands.length} demandas encontradas`);
    } catch (err) {
      console.error("Erro ao buscar demandas:", err);
      demands = []; // Usar array vazio em caso de erro
    }

    // Gerar relatório de status das demandas
    const statusCounts: { [key: string]: number } = {
      'pending': 0,
      'in_progress': 0,
      'completed': 0,
      'cancelled': 0
    };

    demands.forEach(demand => {
      statusCounts[demand.status] = (statusCounts[demand.status] || 0) + 1;
    });

    const demandStatus = Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: demands.length > 0 ? (count / demands.length) * 100 : 0
    }));
    console.log("Relatório de status das demandas:", demandStatus);

    // Gerar relatório de desempenho da equipe
    console.log("Gerando relatório de desempenho da equipe...");
    const performanceMap = new Map<string, TeamPerformanceReport>();

    // Buscar todos os usuários
    try {
      const usersSnapshot = await getDocs(collection(db, 'users'));
      console.log(`${usersSnapshot.size} usuários encontrados`);
      
      usersSnapshot.forEach(doc => {
        const userData = doc.data();
        performanceMap.set(doc.id, {
          userId: doc.id,
          userName: userData.name || 'Usuário sem nome',
          completedDemands: 0,
          averageResponseTime: 0,
          overdueTasks: 0,
          totalDemands: 0
        });
      });

      // Se não houver usuários, adicionar alguns dados simulados
      if (usersSnapshot.empty) {
        console.log("Nenhum usuário encontrado, adicionando dados simulados");
        performanceMap.set('user1', {
          userId: 'user1',
          userName: 'Ana Silva',
          completedDemands: 12,
          averageResponseTime: 24,
          overdueTasks: 2,
          totalDemands: 15
        });
        performanceMap.set('user2', {
          userId: 'user2',
          userName: 'Carlos Oliveira',
          completedDemands: 8,
          averageResponseTime: 36,
          overdueTasks: 1,
          totalDemands: 10
        });
      }
    } catch (err) {
      console.error("Erro ao buscar usuários:", err);
      // Adicionar dados simulados em caso de erro
      performanceMap.set('user1', {
        userId: 'user1',
        userName: 'Ana Silva',
        completedDemands: 12,
        averageResponseTime: 24,
        overdueTasks: 2,
        totalDemands: 15
      });
      performanceMap.set('user2', {
        userId: 'user2',
        userName: 'Carlos Oliveira',
        completedDemands: 8,
        averageResponseTime: 36,
        overdueTasks: 1,
        totalDemands: 10
      });
    }

    // Processar demandas para o relatório de desempenho
    demands.forEach(demand => {
      if (!demand.assignedTo) return;
      
      const userPerformance = performanceMap.get(demand.assignedTo);
      if (!userPerformance) return;

      userPerformance.totalDemands++;
      if (demand.status === 'completed') {
        userPerformance.completedDemands++;
      }
      if (demand.deadline && new Date(demand.deadline) < new Date() && demand.status !== 'completed') {
        userPerformance.overdueTasks++;
      }
    });

    // Calcular média de tempo de resposta (simulado por enquanto)
    performanceMap.forEach(performance => {
      performance.averageResponseTime = Math.floor(Math.random() * 48); // Simulado - horas
    });

    const teamPerformance = Array.from(performanceMap.values());
    console.log("Relatório de desempenho da equipe:", teamPerformance);

    // Gerar relatório de engajamento de cidadãos
    console.log("Gerando relatório de engajamento de cidadãos...");
    let citizenEngagement: CitizenEngagementReport[] = [];
    try {
      citizenEngagement = await generateCitizenEngagementReport(6);
      console.log("Relatório de engajamento de cidadãos:", citizenEngagement);
    } catch (err) {
      console.error("Erro ao gerar relatório de engajamento de cidadãos:", err);
      // Gerar dados simulados em caso de erro
      const months = 6;
      citizenEngagement = [];
      const now = new Date();
      
      for (let i = 0; i < months; i++) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthKey = date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
        
        citizenEngagement.unshift({
          month: monthKey,
          interactions: Math.floor(Math.random() * 100),
          newCitizens: Math.floor(Math.random() * 20),
          activeContacts: Math.floor(Math.random() * 50)
        });
      }
      console.log("Dados simulados de engajamento de cidadãos:", citizenEngagement);
    }

    // Gerar relatório de redes sociais
    console.log("Gerando relatório de redes sociais...");
    let socialMedia: SocialMediaReport[] = [];
    try {
      socialMedia = await generateSocialMediaReport();
      console.log("Relatório de redes sociais:", socialMedia);
    } catch (err) {
      console.error("Erro ao gerar relatório de redes sociais:", err);
      // Gerar dados simulados em caso de erro
      socialMedia = [
        {
          platform: 'Facebook',
          followers: 5200,
          engagement: 3.2,
          reach: 12000,
          sentiment: { positive: 65, neutral: 25, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        },
        {
          platform: 'Instagram',
          followers: 8500,
          engagement: 4.7,
          reach: 20000,
          sentiment: { positive: 70, neutral: 20, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        }
      ];
      console.log("Dados simulados de redes sociais:", socialMedia);
    }

    const result = {
      dateRange: {
        start: start.toISOString(),
        end: end.toISOString()
      },
      demandStatus,
      teamPerformance,
      citizenEngagement,
      socialMedia
    };
    
    console.log("Relatório completo gerado com sucesso");
    return result;
  } catch (error) {
    console.error("Error generating full report:", error);
    
    // Em caso de erro, retornar dados simulados
    console.log("Retornando dados simulados devido a erro");
    const simulatedReport = {
      dateRange: {
        start: start.toISOString(),
        end: end.toISOString()
      },
      demandStatus: [
        { status: 'pending', count: 5, percentage: 25 },
        { status: 'in_progress', count: 8, percentage: 40 },
        { status: 'completed', count: 6, percentage: 30 },
        { status: 'cancelled', count: 1, percentage: 5 }
      ],
      teamPerformance: [
        {
          userId: 'user1',
          userName: 'Ana Silva',
          completedDemands: 12,
          averageResponseTime: 24,
          overdueTasks: 2,
          totalDemands: 15
        },
        {
          userId: 'user2',
          userName: 'Carlos Oliveira',
          completedDemands: 8,
          averageResponseTime: 36,
          overdueTasks: 1,
          totalDemands: 10
        }
      ],
      citizenEngagement: generateSimulatedCitizenEngagement(6),
      socialMedia: [
        {
          platform: 'Facebook',
          followers: 5200,
          engagement: 3.2,
          reach: 12000,
          sentiment: { positive: 65, neutral: 25, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        },
        {
          platform: 'Instagram',
          followers: 8500,
          engagement: 4.7,
          reach: 20000,
          sentiment: { positive: 70, neutral: 20, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        },
        {
          platform: 'Twitter',
          followers: 3200,
          engagement: 2.8,
          reach: 9000,
          sentiment: { positive: 55, neutral: 30, negative: 15 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        },
        {
          platform: 'YouTube',
          followers: 1800,
          engagement: 3.5,
          reach: 7500,
          sentiment: { positive: 60, neutral: 30, negative: 10 },
          posts: {
            total: 0,
            engagementRate: 0
          }
        }
      ]
    };
    return simulatedReport;
  }
};

// Função auxiliar para gerar dados simulados de engajamento de cidadãos
function generateSimulatedCitizenEngagement(months: number): CitizenEngagementReport[] {
  const result: CitizenEngagementReport[] = [];
  const now = new Date();
  
  for (let i = 0; i < months; i++) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthKey = date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
    
    result.unshift({
      month: monthKey,
      interactions: Math.floor(Math.random() * 100),
      newCitizens: Math.floor(Math.random() * 20),
      activeContacts: Math.floor(Math.random() * 50)
    });
  }
  
  return result;
}

// --- Social Media Collections ---
const SOCIAL_MEDIA_COLLECTION = 'social_media';
const POLITICIAN_SOCIAL_COLLECTION = 'politician_social';
const SOCIAL_MEDIA_POSTS_COLLECTION = 'social_media_posts';

export const savePoliticianSocialMediaAccounts = async (
  accounts: PoliticianSocialMedia[],
  politicianId: string = 'main_profile'
): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, POLITICIAN_SOCIAL_COLLECTION, politicianId);
    await setDoc(docRef, {
      accounts,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error saving politician social media accounts:", error);
    throw error;
  }
};

export const getPoliticianSocialMediaAccounts = async (
  politicianId: string = 'main_profile'
): Promise<PoliticianSocialMedia[]> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, POLITICIAN_SOCIAL_COLLECTION, politicianId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data();
      return data.accounts || [];
    }
    return [];
  } catch (error) {
    console.error("Error getting politician social media accounts:", error);
    throw error;
  }
};

export const getSocialMediaAnalytics = async (): Promise<SocialMediaReport[]> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const collectionRef = collection(db, SOCIAL_MEDIA_COLLECTION);
    const snapshot = await getDocs(collectionRef);
    
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        platform: doc.id,
        followers: data.followers || 0,
        engagement: data.engagement || 0,
        reach: data.reach || 0,
        sentiment: data.sentiment || { positive: 0, neutral: 0, negative: 0 },
        posts: data.posts || {  // Adicionando a propriedade posts que estava faltando
          total: 0,
          engagementRate: 0
        }
      };
    });
  } catch (error) {
    console.error("Error getting social media analytics:", error);
    throw error;
  }
};

// --- Social Media Posts Functions ---
export const saveSocialMediaPost = async (post: SocialMediaPost): Promise<void> => {
  if (!db || !auth.currentUser) throw new Error("Firestore not initialized or user not authenticated.");

  try {
    const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
    const userData = userDoc.data();
    const masterId = userData?.role === UserRole.MASTER ? auth.currentUser.uid : userData?.masterId || auth.currentUser.uid;

    const postData = {
      ...post,
      masterId,
      createdBy: auth.currentUser.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    await addDoc(collection(db, SOCIAL_MEDIA_POSTS_COLLECTION), postData);
    console.log('Post salvo com sucesso no Firestore');
  } catch (error) {
    console.error("Error saving social media post:", error);
    throw error;
  }
};

export const getSocialMediaPosts = async (): Promise<SocialMediaPost[]> => {
  if (!db || !auth.currentUser) throw new Error("Firestore not initialized or user not authenticated.");

  try {
    const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
    const userData = userDoc.data();
    const masterId = userData?.role === UserRole.MASTER ? auth.currentUser.uid : userData?.masterId || auth.currentUser.uid;

    const postsCollection = collection(db, SOCIAL_MEDIA_POSTS_COLLECTION);
    const q = query(
      postsCollection,
      where('masterId', '==', masterId),
      orderBy('createdAt', 'desc')
    );
    const snapshot = await getDocs(q);

    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: convertToISOString(data.createdAt),
        updatedAt: convertToISOString(data.updatedAt),
        scheduledFor: data.scheduledFor || undefined,
        publishedAt: data.publishedAt || undefined
      } as SocialMediaPost;
    });
  } catch (error) {
    console.error("Error getting social media posts:", error);
    throw error;
  }
};

export const updateSocialMediaPost = async (postId: string, updates: Partial<SocialMediaPost>): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");

  try {
    const docRef = doc(db, SOCIAL_MEDIA_POSTS_COLLECTION, postId);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error("Error updating social media post:", error);
    throw error;
  }
};

export const deleteSocialMediaPost = async (postId: string): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");

  try {
    const docRef = doc(db, SOCIAL_MEDIA_POSTS_COLLECTION, postId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting social media post:", error);
    throw error;
  }
};

// --- Team Members Functions (Firestore) ---
const TEAM_MEMBERS_COLLECTION = 'team_members';

export const getTeamMembers = async (): Promise<TeamMember[]> => {
  if (!db || !auth.currentUser) throw new Error("Firestore not initialized or user not authenticated.");
  try {
    console.log("Buscando membros da equipe");
    
    // Primeiro, tentar buscar da coleção team_members
    const teamMembersSnapshot = await getDocs(collection(db, TEAM_MEMBERS_COLLECTION));
    console.log(`${teamMembersSnapshot.size} membros encontrados na coleção team_members`);
    
    if (teamMembersSnapshot.size > 0) {
      // Se existirem membros na coleção específica, use-os
      const teamMembers: TeamMember[] = [];
      
      teamMembersSnapshot.forEach(doc => {
        const data = doc.data();
        teamMembers.push({
          id: doc.id,
          userId: data.userId || doc.id,
          name: data.name,
          email: data.email,
          roleInTeam: data.roleInTeam,
          permissions: data.permissions || [],
          joinedAt: data.joinedAt || convertTimestampToISO(data.createdAt as Timestamp) || new Date().toISOString()
        });
      });
      
      console.log(`Retornando ${teamMembers.length} membros da coleção team_members`);
      return teamMembers;
    }
    
    // Se não houver membros na coleção específica, buscar da coleção users
    console.log("Nenhum membro encontrado na coleção team_members, buscando da coleção users");
    const usersSnapshot = await getDocs(collection(db, 'users'));
    console.log(`${usersSnapshot.size} usuários encontrados`);
    
    const teamMembers: TeamMember[] = [];
    
    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      
      // Extrair nome do usuário de várias fontes possíveis
      const name = userData.name || 
                   userData.displayName || 
                   (userData.email ? userData.email.split('@')[0] : null) || 
                   `Usuário ID: ${doc.id.substring(0, 8)}`;
      
      const member: TeamMember = {
        id: doc.id,
        userId: doc.id,
        name: name,
        roleInTeam: userData.role || 'member',
        permissions: [],
        joinedAt: userData.createdAt || new Date().toISOString()
      };
      
      // Adicionar email apenas se existir
      if (userData.email) {
        member.email = userData.email;
      }
      
      teamMembers.push(member);
      
      console.log(`Membro da equipe processado: ${name} (${doc.id}), role: ${userData.role || 'member'}`);
    });
    
    // Se não houver membros, adicionar o usuário atual
    if (teamMembers.length === 0) {
      const currentUser = auth.currentUser;
      const member: TeamMember = {
        id: currentUser.uid,
        userId: currentUser.uid,
        name: currentUser.displayName || currentUser.email?.split('@')[0] || 'Usuário atual',
        roleInTeam: 'master',
        permissions: ['view_demands', 'edit_demands', 'manage_citizens', 'manage_agenda', 'view_documents', 'manage_social_media', 'view_reports', 'manage_team'],
        joinedAt: new Date().toISOString()
      };
      
      // Adicionar email apenas se existir
      if (currentUser.email) {
        member.email = currentUser.email;
      }
      
      teamMembers.push(member);
      
      console.log(`Adicionado apenas o usuário atual como membro da equipe: ${member.name}`);
    }
    
    console.log(`Retornando ${teamMembers.length} membros da coleção users`);
    return teamMembers;
  } catch (error) {
    console.error("Error getting team members:", error);
    
    // Em caso de erro, retornar array vazio ou dados de exemplo
    return [];
  }
};

export const addTeamMember = async (memberData: Omit<TeamMember, 'id' | 'createdAt' | 'updatedAt'>): Promise<TeamMember> => {
  if (!db || !auth.currentUser) throw new Error("Não autenticado");
  
  try {
    const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
    const userData = userDoc.data();
    let masterId = userData?.role === UserRole.MASTER ? auth.currentUser.uid : userData?.masterId || auth.currentUser.uid;

    const docRef = await addDoc(collection(db, TEAM_MEMBERS_COLLECTION), {
      ...memberData,
      masterId,
      createdBy: auth.currentUser.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    const newDocSnap = await getDoc(docRef);
    const newData = newDocSnap.data();
    
    return {
      id: newDocSnap.id,
      userId: newData?.userId,
      name: newData?.name,
      roleInTeam: newData?.roleInTeam,
      permissions: newData?.permissions || [],
      joinedAt: newData?.joinedAt,
      createdAt: convertTimestampToISO(newData?.createdAt as Timestamp),
      updatedAt: convertTimestampToISO(newData?.updatedAt as Timestamp),
    } as TeamMember;
  } catch (error) {
    console.error("Error adding team member:", error);
    throw error;
  }
};

export const updateTeamMember = async (memberId: string, memberData: Partial<Omit<TeamMember, 'id' | 'createdAt' | 'createdBy'>>): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, TEAM_MEMBERS_COLLECTION, memberId);
    await updateDoc(docRef, {
      ...memberData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error updating team member:", error);
    throw error;
  }
};

export const deleteTeamMember = async (memberId: string): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, TEAM_MEMBERS_COLLECTION, memberId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting team member:", error);
    throw error;
  }
};

// --- Services Functions (Firestore) ---
const SERVICES_COLLECTION = 'services';

export const getServices = async (): Promise<Service[]> => {
  if (!db || !auth.currentUser) throw new Error("Não autenticado");
  try {
    console.log("Firebase - Buscando serviços");
    const servicesCollection = collection(db, SERVICES_COLLECTION);
    const snapshot = await getDocs(servicesCollection);
    
    const services = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        description: data.description,
        category: data.category,
        status: data.status,
        priority: data.priority,
        deadline: data.deadline,
        createdAt: convertTimestampToISO(data.createdAt),
        updatedAt: convertTimestampToISO(data.updatedAt),
        createdBy: data.createdBy,
        assignedTo: data.assignedTo,
        completedAt: data.completedAt ? convertTimestampToISO(data.completedAt) : undefined,
        notes: data.notes,
        attachments: data.attachments
      } as Service;
    });

    console.log("Firebase - Serviços encontrados:", services.length);
    return services;
  } catch (error) {
    console.error("Firebase - Erro ao buscar serviços:", error);
    throw error;
  }
};

export const addService = async (
  serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, 
  userId: string
): Promise<Service> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = await addDoc(collection(db, SERVICES_COLLECTION), {
      ...serviceData,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    const newDocSnap = await getDoc(docRef);
    const newData = newDocSnap.data();
    
    return {
      id: newDocSnap.id,
      ...newData,
      createdAt: convertTimestampToISO(newData?.createdAt as Timestamp),
      updatedAt: convertTimestampToISO(newData?.updatedAt as Timestamp),
    } as Service;
  } catch (error) {
    console.error("Error adding service:", error);
    throw error;
  }
};

export const updateService = async (
  serviceId: string, 
  serviceData: Partial<Omit<Service, 'id' | 'createdAt' | 'createdBy'>>
): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, SERVICES_COLLECTION, serviceId);
    await updateDoc(docRef, {
      ...serviceData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error updating service:", error);
    throw error;
  }
};

export const deleteService = async (serviceId: string): Promise<void> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = doc(db, SERVICES_COLLECTION, serviceId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting service:", error);
    throw error;
  }
};

// --- Bulk Messages Functions (Firestore) ---
const BULK_MESSAGES_COLLECTION = 'bulk_messages';

export const addBulkMessage = async (
  messageData: Omit<BulkMessage, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, 
  userId: string
): Promise<BulkMessage> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const docRef = await addDoc(collection(db, BULK_MESSAGES_COLLECTION), {
      ...messageData,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    const newDocSnap = await getDoc(docRef);
    const newData = newDocSnap.data();
    
    return {
      id: newDocSnap.id,
      ...newData,
      createdAt: convertTimestampToISO(newData?.createdAt as Timestamp),
      updatedAt: convertTimestampToISO(newData?.updatedAt as Timestamp),
    } as BulkMessage;
  } catch (error) {
    console.error("Error adding bulk message:", error);
    throw error;
  }
};

export const getBulkMessages = async (): Promise<BulkMessage[]> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    const messagesRef = collection(db, BULK_MESSAGES_COLLECTION);
    const snapshot = await getDocs(messagesRef);
    
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: convertTimestampToISO(data.createdAt as Timestamp),
        updatedAt: convertTimestampToISO(data.updatedAt as Timestamp),
        sentAt: data.sentAt ? convertTimestampToISO(data.sentAt as Timestamp) : undefined,
        scheduledFor: data.scheduledFor ? convertTimestampToISO(data.scheduledFor as Timestamp) : undefined,
      } as BulkMessage;
    });
  } catch (error) {
    console.error("Error getting bulk messages:", error);
    throw error;
  }
};

// Função para salvar uma mensagem como documento
export const saveBulkMessageAsDocument = async (
  message: BulkMessage, 
  userId: string
): Promise<Document> => {
  if (!db) throw new Error("Firestore not initialized.");
  try {
    // Criar conteúdo do documento (pode ser HTML, texto, etc.)
    const messageContent = `
      <h1>${message.title}</h1>
      <p>${message.body}</p>
      <p>Enviado para ${message.recipients.length} destinatários em ${new Date().toLocaleDateString()}</p>
    `;
    
    // Converter para Blob
    const blob = new Blob([messageContent], { type: 'text/html' });
    const file = new File([blob], `Mensagem - ${message.title}.html`, { type: 'text/html' });
    
    // Caminho no storage
    const storagePath = `documents/${userId}/${Date.now()}_${file.name}`;
    
    // Upload do arquivo
    const downloadURL = await uploadFileToStorage(file, storagePath);
    
    // Salvar metadados do documento
    const documentMetadata: Omit<Document, 'id' | 'createdAt'> = {
      name: `Mensagem - ${message.title}`,
      type: file.type,
      url: downloadURL,
      storagePath: storagePath,
      uploadedBy: userId,
      size: file.size,
    };
    
    return await addDocumentMetadata(documentMetadata);
  } catch (error) {
    console.error("Error saving bulk message as document:", error);
    throw error;
  }
};

// Export auth, db, storage for potential direct use if needed, though functions are preferred
export { auth as firebaseAuth, db as firestoreDb, storage as firebaseStorage };
