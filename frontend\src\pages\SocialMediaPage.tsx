import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { PoliticianSocialMedia, SocialMediaAnalytics, SocialMediaPost } from '../types';
import {
  getPoliticianProfile,
  savePoliticianSocialMediaAccounts,
  getPoliticianSocialMediaAccounts,
  getSocialMediaAnalytics,
  saveSocialMediaPost
} from '../services/firebaseService';
import PostHistory from '../components/social-media/PostHistory';
import MediaUpload from '../components/social-media/MediaUpload';
import PostScheduler from '../components/social-media/PostScheduler';

const MAX_MEDIA_FILES = 4;

const socialIcons = {
  Facebook: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
    <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
  </svg>,
  Instagram: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-pink-500" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>,
  Twitter: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
  </svg>,
  TikTok: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
  </svg>
};

const SocialMediaPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [socialAccounts, setSocialAccounts] = useState<PoliticianSocialMedia[]>([]);
  const [socialAnalytics, setSocialAnalytics] = useState<SocialMediaAnalytics[]>([]);
  const [postContent, setPostContent] = useState('');
  const [selectedMedia, setSelectedMedia] = useState<File[]>([]);
  const [mediaPreviews, setMediaPreviews] = useState<string[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState<'compose' | 'history' | 'analytics'>('compose');
  const [isPosting, setIsPosting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaPreviewsRef = useRef<string[]>([]);

  // Atualiza o ref quando os previews mudam
  useEffect(() => {
    mediaPreviewsRef.current = mediaPreviews;
  }, [mediaPreviews]);

  // Limpa as URLs dos previews quando o componente é desmontado ou a página é fechada
  useEffect(() => {
    const cleanupPreviews = () => {
      mediaPreviewsRef.current.forEach(url => {
        try {
          URL.revokeObjectURL(url);
        } catch (err) {
          console.warn('Erro ao limpar URL de preview:', err);
        }
      });
    };

    window.addEventListener('beforeunload', cleanupPreviews);
    
    return () => {
      window.removeEventListener('beforeunload', cleanupPreviews);
      cleanupPreviews();
    };
  }, []);

  const loadSocialData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const [profile, accounts, analytics] = await Promise.all([
        getPoliticianProfile(),
        getPoliticianSocialMediaAccounts(),
        getSocialMediaAnalytics()
      ]);

      // Se temos perfil mas não temos contas conectadas, vamos criar as contas com os dados do perfil
      if (profile?.socialMedia && (!accounts || accounts.length === 0)) {
        const newAccounts: PoliticianSocialMedia[] = [];
        
        if (profile.socialMedia.facebook) {
          newAccounts.push({
            platform: 'Facebook',
            username: profile.socialMedia.facebook,
            url: profile.socialMedia.facebook.startsWith('http') ? profile.socialMedia.facebook : `https://facebook.com/${profile.socialMedia.facebook}`,
            connected: false,
          });
        }
        
        if (profile.socialMedia.instagram) {
          newAccounts.push({
            platform: 'Instagram',
            username: profile.socialMedia.instagram,
            url: `https://instagram.com/${profile.socialMedia.instagram.replace('@', '')}`,
            connected: false,
          });
        }
        
        if (profile.socialMedia.twitter) {
          newAccounts.push({
            platform: 'Twitter',
            username: profile.socialMedia.twitter,
            url: `https://twitter.com/${profile.socialMedia.twitter.replace('@', '')}`,
            connected: false,
          });
        }
        
        if (profile.socialMedia.tiktok) {
          newAccounts.push({
            platform: 'TikTok',
            username: profile.socialMedia.tiktok,
            url: `https://tiktok.com/@${profile.socialMedia.tiktok.replace('@', '')}`,
            connected: false,
          });
        }

        if (newAccounts.length > 0) {
          await savePoliticianSocialMediaAccounts(newAccounts);
          setSocialAccounts(newAccounts);
        }
      } else {
        setSocialAccounts(accounts || []);
      }
      
      setSocialAnalytics(analytics);
    } catch (err) {
      console.error('Erro ao carregar dados de redes sociais:', err);
      setError('Falha ao carregar dados das redes sociais. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSocialData();
  }, [loadSocialData]);

  const handleConnect = async (platform: string) => {
    try {
      setIsLoading(true);
      
      // Busca o perfil do político para validar os dados
      const profile = await getPoliticianProfile();
      if (!profile?.socialMedia) {
        throw new Error('Perfil do político não tem dados de redes sociais configurados.');
      }

      let username = '';
      let url = '';
      
      switch (platform) {
        case 'Facebook':
          username = profile.socialMedia.facebook || '';
          if (!username) throw new Error('URL do Facebook não configurada no perfil do político.');
          url = username.startsWith('http') ? username : `https://facebook.com/${username}`;
          break;
        
        case 'Instagram':
          username = profile.socialMedia.instagram || '';
          if (!username) throw new Error('Usuário do Instagram não configurado no perfil do político.');
          username = username.replace('@', '');
          url = `https://instagram.com/${username}`;
          break;
        
        case 'Twitter':
          username = profile.socialMedia.twitter || '';
          if (!username) throw new Error('Usuário do Twitter não configurado no perfil do político.');
          username = username.replace('@', '');
          url = `https://twitter.com/${username}`;
          break;
        
        case 'TikTok':
          username = profile.socialMedia.tiktok || '';
          if (!username) throw new Error('Usuário do TikTok não configurado no perfil do político.');
          username = username.replace('@', '');
          url = `https://tiktok.com/@${username}`;
          break;
        
        default:
          throw new Error('Plataforma não suportada.');
      }

      // Em produção, aqui faríamos a autenticação OAuth com a plataforma
      const mockConnection: PoliticianSocialMedia = {
        platform,
        username,
        url,
        connected: true,
        lastSync: new Date().toISOString()
      };

      const updatedAccounts = [
        ...socialAccounts.filter(acc => acc.platform !== platform),
        mockConnection
      ];

      await savePoliticianSocialMediaAccounts(updatedAccounts);
      setSocialAccounts(updatedAccounts);
      alert(`Conta ${platform} conectada com sucesso!`);
    } catch (err) {
      console.error('Erro ao conectar rede social:', err);
      setError(err instanceof Error ? err.message : 'Falha ao conectar rede social. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async (platform: string) => {
    if (window.confirm(`Tem certeza que deseja desconectar ${platform}?`)) {
      try {
        setIsLoading(true);
        const updatedAccounts = socialAccounts.filter(acc => acc.platform !== platform);
        await savePoliticianSocialMediaAccounts(updatedAccounts);
        setSocialAccounts(updatedAccounts);
        alert(`Conta ${platform} desconectada com sucesso!`);
      } catch (err) {
        console.error('Erro ao desconectar rede social:', err);
        setError('Falha ao desconectar rede social. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handlePost = async (scheduledDate?: Date) => {
    if (!postContent.trim() && selectedMedia.length === 0) {
      setError('É necessário incluir texto ou mídia para fazer uma publicação.');
      return;
    }

    if (selectedPlatforms.size === 0) {
      setError('Selecione pelo menos uma rede social para publicar.');
      return;
    }

    setIsPosting(true);
    setError(null);

    try {
      // Em produção, aqui faríamos o upload das mídias e publicação em cada rede
      const connectedAccounts = socialAccounts.filter(acc =>
        acc.connected && selectedPlatforms.has(acc.platform)
      );

      if (connectedAccounts.length === 0) {
        throw new Error('Nenhuma das redes sociais selecionadas está conectada.');
      }

      // Simula o tempo de upload e publicação
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Criar post no histórico
      const newPost: SocialMediaPost = {
        id: Date.now().toString(),
        content: postContent,
        mediaUrls: mediaPreviews, // Em produção, seriam URLs do Firebase Storage
        platforms: Array.from(selectedPlatforms),
        status: scheduledDate ? 'scheduled' : 'published',
        scheduledFor: scheduledDate?.toISOString(),
        publishedAt: scheduledDate ? undefined : new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Salvar no Firestore
      await saveSocialMediaPost(newPost);

      // Limpa o formulário após sucesso
      clearForm();

      const message = scheduledDate
        ? `Publicação agendada com sucesso para ${connectedAccounts.length} rede(s) social(is)!`
        : `Publicação realizada com sucesso em ${connectedAccounts.length} rede(s) social(is)!`;

      alert(message);
    } catch (err) {
      console.error('Erro ao fazer publicação:', err);
      setError(err instanceof Error ? err.message : 'Falha ao fazer a publicação. Tente novamente.');
    } finally {
      setIsPosting(false);
    }
  };

  const clearForm = () => {
    setPostContent('');
    setSelectedMedia([]);
    mediaPreviewsRef.current.forEach(url => URL.revokeObjectURL(url));
    setMediaPreviews([]);
    setSelectedPlatforms(new Set());
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleRepost = (post: SocialMediaPost) => {
    setPostContent(post.content);
    setSelectedPlatforms(new Set(post.platforms));
    setActiveTab('compose');
    // TODO: Recarregar mídia se necessário
  };

  const handleMediaChange = (files: File[], previews: string[]) => {
    setSelectedMedia(files);
    setMediaPreviews(previews);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-neutral-light">
          Redes Sociais
        </h1>
      </div>

      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md">
          {error}
        </div>
      )}

      {/* Navegação por Abas */}
      <div className="border-b border-gray-200 dark:border-neutral-medium">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'compose', label: 'Criar Post', icon: '✏️' },
            { key: 'history', label: 'Histórico', icon: '📋' },
            { key: 'analytics', label: 'Analytics', icon: '📊' }
          ].map(({ key, label, icon }) => (
            <button
              key={key}
              type="button"
              onClick={() => setActiveTab(key as 'compose' | 'history' | 'analytics')}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === key
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-neutral-medium dark:hover:text-neutral-light'
              }`}
            >
              <span>{icon}</span>
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Conteúdo das Abas */}
      {activeTab === 'compose' && (
        <div className="space-y-6">
          <Card title="Contas Conectadas">
        <ul className="divide-y divide-gray-200 dark:divide-neutral-dark">
          {socialAccounts.map(account => (
            <li key={account.platform} className="py-4 flex items-center justify-between">
              <div className="flex items-center">
                {socialIcons[account.platform as keyof typeof socialIcons]}
                <div className="ml-3">
                  <p className="text-sm font-medium text-neutral-dark dark:text-neutral-light">{account.platform}</p>
                  <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">{account.username}</p>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleDisconnect(account.platform)}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500"
              >
                Desconectar
              </Button>
            </li>
          ))}
        </ul>

        {socialAccounts.length === 0 && (
          <p className="text-center py-4 text-gray-500 dark:text-neutral-DEFAULT">
            Nenhuma rede social conectada.
          </p>
        )}

        <div className="mt-4 flex flex-wrap gap-2">
          <Button
            onClick={() => handleConnect('Instagram')}
            disabled={socialAccounts.some(acc => acc.platform === 'Instagram')}
            leftIcon={socialIcons.Instagram}
          >
            Conectar Instagram
          </Button>
          <Button
            onClick={() => handleConnect('Facebook')}
            disabled={socialAccounts.some(acc => acc.platform === 'Facebook')}
            leftIcon={socialIcons.Facebook}
          >
            Conectar Facebook
          </Button>
          <Button
            onClick={() => handleConnect('Twitter')}
            disabled={socialAccounts.some(acc => acc.platform === 'Twitter')}
            leftIcon={socialIcons.Twitter}
          >
            Conectar Twitter/X
          </Button>
          <Button
            onClick={() => handleConnect('TikTok')}
            disabled={socialAccounts.some(acc => acc.platform === 'TikTok')}
            leftIcon={socialIcons.TikTok}
          >
            Conectar TikTok
          </Button>
        </div>

        {/* Instruções para configurar redes sociais */}
        {socialAccounts.length === 0 && (
          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              📱 Configure suas Redes Sociais
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
              Para usar esta funcionalidade, primeiro configure suas redes sociais no seu perfil:
            </p>
            <ol className="text-sm text-blue-700 dark:text-blue-300 list-decimal list-inside space-y-1 mb-3">
              <li>Vá para <strong>Configurações → Perfil do Político</strong></li>
              <li>Preencha os campos de redes sociais (Instagram, Facebook, Twitter, TikTok)</li>
              <li>Salve as alterações</li>
              <li>Volte aqui e conecte suas contas</li>
            </ol>
            <Button
              variant="primary"
              size="sm"
              onClick={() => window.location.href = '/politician-profile'}
            >
              🔧 Ir para Perfil do Político
            </Button>
          </div>
        )}
      </Card>

          {/* Novo componente de upload de mídia */}
          <MediaUpload
            selectedMedia={selectedMedia}
            mediaPreviews={mediaPreviews}
            onMediaChange={handleMediaChange}
            maxFiles={MAX_MEDIA_FILES}
          />

          <Card title="Criar Publicação">
            <div className="space-y-4">
              <textarea
                id="postContent"
                name="postContent"
                rows={5}
                className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-neutral-medium rounded-md
                         focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light
                         p-2 bg-white dark:bg-neutral-dark dark:text-neutral-light
                         placeholder-gray-400 dark:placeholder-neutral-medium"
                placeholder="Escreva sua postagem aqui..."
                value={postContent}
                onChange={e => setPostContent(e.target.value)}
              />

              <div className="mt-4">
                <fieldset className="space-y-2">
                  <legend className="text-sm font-medium text-gray-700 dark:text-neutral-light">Publicar em:</legend>

                  <div className="flex flex-wrap gap-3">
                    {socialAccounts.length === 0 ? (
                      <div className="text-sm text-gray-500 dark:text-neutral-medium py-2">
                        Configure suas redes sociais no perfil para poder publicar.
                      </div>
                    ) : (
                      socialAccounts.map(account => (
                        <label key={account.platform} className="inline-flex items-center cursor-pointer p-2 border border-gray-200 dark:border-neutral-medium rounded-lg hover:bg-gray-50 dark:hover:bg-neutral-dark transition-colors">
                          <input
                            type="checkbox"
                            title={`Publicar no ${account.platform}`}
                            aria-label={`Publicar no ${account.platform}`}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                            checked={selectedPlatforms.has(account.platform)}
                            onChange={(e) => {
                              const newPlatforms = new Set(selectedPlatforms);
                              if (e.target.checked) {
                                newPlatforms.add(account.platform);
                              } else {
                                newPlatforms.delete(account.platform);
                              }
                              setSelectedPlatforms(newPlatforms);
                            }}
                            disabled={!account.connected}
                          />
                          <div className="ml-2">
                            <div className="flex items-center gap-1">
                              {socialIcons[account.platform as keyof typeof socialIcons]}
                              <span className="text-sm font-medium text-gray-700 dark:text-neutral-light">
                                {account.platform}
                              </span>
                            </div>
                            <div className="text-xs text-gray-500 dark:text-neutral-medium">
                              {account.connected ? (
                                <span className="text-green-600 dark:text-green-400">✓ Conectado</span>
                              ) : (
                                <span className="text-red-500">⚠ Desconectado</span>
                              )}
                            </div>
                          </div>
                        </label>
                      ))
                    )}
                  </div>

                  {selectedPlatforms.size > 0 && (
                    <div className="mt-3 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <div className="text-sm text-green-700 dark:text-green-300">
                        <strong>Publicar em:</strong> {Array.from(selectedPlatforms).join(', ')}
                      </div>
                    </div>
                  )}
                </fieldset>
              </div>
            </div>
          </Card>

          {/* Novo componente de agendamento */}
          <PostScheduler
            onSchedule={(date) => handlePost(date)}
            onPublishNow={() => handlePost()}
            isLoading={isPosting}
            disabled={
              (!postContent.trim() && selectedMedia.length === 0) ||
              socialAccounts.length === 0 ||
              selectedPlatforms.size === 0
            }
          />
        </div>
      )}

      {/* Aba de Histórico */}
      {activeTab === 'history' && (
        <PostHistory onRepost={handleRepost} />
      )}

      {/* Aba de Analytics */}
      {activeTab === 'analytics' && socialAccounts.length > 0 && (
        <Card title="Análise de Redes Sociais">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {socialAnalytics.map(analytics => (
              <div
                key={analytics.platform}
                className="p-4 border dark:border-neutral-medium rounded-lg"
              >
                <h3 className="font-semibold text-neutral-dark dark:text-neutral-light mb-2">
                  {analytics.platform}
                </h3>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-neutral-DEFAULT">Seguidores:</span>
                    <span className="font-medium text-neutral-dark dark:text-neutral-light">
                      {analytics.followers.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-neutral-DEFAULT">Engajamento:</span>
                    <span className="font-medium text-neutral-dark dark:text-neutral-light">
                      {analytics.engagement.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-neutral-DEFAULT">Alcance:</span>
                    <span className="font-medium text-neutral-dark dark:text-neutral-light">
                      {analytics.reach.toLocaleString()}
                    </span>
                  </div>
                  <div className="mt-2 pt-2 border-t dark:border-neutral-medium">
                    <div className="flex gap-2">
                      <div className="flex-1 bg-green-100 dark:bg-green-900/30 rounded px-2 py-1">
                        <span className="text-xs text-green-600 dark:text-green-400">
                          {analytics.sentiment.positive.toFixed(1)}% positivo
                        </span>
                      </div>
                      <div className="flex-1 bg-yellow-100 dark:bg-yellow-900/30 rounded px-2 py-1">
                        <span className="text-xs text-yellow-600 dark:text-yellow-400">
                          {analytics.sentiment.neutral.toFixed(1)}% neutro
                        </span>
                      </div>
                      <div className="flex-1 bg-red-100 dark:bg-red-900/30 rounded px-2 py-1">
                        <span className="text-xs text-red-600 dark:text-red-400">
                          {analytics.sentiment.negative.toFixed(1)}% negativo
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default SocialMediaPage;