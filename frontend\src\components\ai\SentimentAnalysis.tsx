import React, { useState } from 'react';
import { ICONS } from '../../constants';
import { usePlan } from '../../hooks/usePlan';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Textarea } from '../ui/Textarea';

interface SentimentResult {
  text: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  scores: {
    positive: number;
    negative: number;
    neutral: number;
  };
}

const SentimentAnalysis: React.FC = () => {
  const { canUseAIFeature, incrementAIUsage, getAIFeatureUsage, currentPlan } = usePlan();
  const [text, setText] = useState('');
  const [result, setResult] = useState<SentimentResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const canUseFeature = canUseAIFeature('text-analysis');
  const usage = getAIFeatureUsage('text-analysis');

  const handleAnalyze = async () => {
    if (!text.trim()) {
      setError('Por favor, insira um texto para análise');
      return;
    }

    if (!canUseFeature) {
      setError('Funcionalidade não disponível no seu plano atual');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      // Incrementar uso antes da análise
      const canProceed = await incrementAIUsage('text-analysis');
      if (!canProceed) {
        setError('Limite de uso atingido para este mês');
        return;
      }

      // Simular análise de sentimento (em produção, chamar API real)
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Análise simples baseada em palavras-chave
      const positiveWords = ['bom', 'ótimo', 'excelente', 'maravilhoso', 'perfeito', 'adorei', 'parabéns', 'obrigado'];
      const negativeWords = ['ruim', 'péssimo', 'horrível', 'terrível', 'odeio', 'problema', 'reclamação', 'insatisfeito'];
      
      const words = text.toLowerCase().split(/\s+/);
      let positiveScore = 0;
      let negativeScore = 0;
      
      words.forEach(word => {
        if (positiveWords.some(pw => word.includes(pw))) positiveScore++;
        if (negativeWords.some(nw => word.includes(nw))) negativeScore++;
      });
      
      const total = positiveScore + negativeScore;
      let sentiment: 'positive' | 'negative' | 'neutral';
      let confidence: number;
      let scores: { positive: number; negative: number; neutral: number };
      
      if (total === 0) {
        sentiment = 'neutral';
        confidence = 0.6;
        scores = { positive: 0.33, negative: 0.33, neutral: 0.34 };
      } else if (positiveScore > negativeScore) {
        sentiment = 'positive';
        confidence = 0.7 + (positiveScore / total) * 0.3;
        scores = { positive: 0.7, negative: 0.15, neutral: 0.15 };
      } else if (negativeScore > positiveScore) {
        sentiment = 'negative';
        confidence = 0.7 + (negativeScore / total) * 0.3;
        scores = { positive: 0.15, negative: 0.7, neutral: 0.15 };
      } else {
        sentiment = 'neutral';
        confidence = 0.5;
        scores = { positive: 0.3, negative: 0.3, neutral: 0.4 };
      }

      setResult({
        text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
        sentiment,
        confidence,
        scores
      });

    } catch (error) {
      console.error('Erro na análise:', error);
      setError('Erro ao processar análise. Tente novamente.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return <div className="w-5 h-5 text-green-500">{ICONS.TRENDING_UP}</div>;
      case 'negative':
        return <div className="w-5 h-5 text-red-500">{ICONS.TRENDING_DOWN}</div>;
      default:
        return <div className="w-5 h-5 text-yellow-500">{ICONS.ALERT_CIRCLE}</div>;
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'negative':
        return 'text-red-700 bg-red-50 border-red-200';
      default:
        return 'text-yellow-700 bg-yellow-50 border-yellow-200';
    }
  };

  const getSentimentLabel = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return 'Positivo';
      case 'negative':
        return 'Negativo';
      default:
        return 'Neutro';
    }
  };

  // Se não tem acesso à funcionalidade
  if (!canUseFeature) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-gray-100 rounded-full p-3">
              <div className="w-8 h-8 text-gray-400">
                {ICONS.LOCK}
              </div>
            </div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Análise de Sentimento
          </h3>
          <p className="text-gray-600 mb-4">
            Esta funcionalidade está disponível nos planos Padrão e Profissional.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-5 h-5 text-blue-600">
                {ICONS.CROWN}
              </div>
              <span className="font-medium text-blue-900">Upgrade Necessário</span>
            </div>
            <p className="text-sm text-blue-700">
              Faça upgrade para o plano Padrão ou Profissional para usar a análise de sentimento com IA.
            </p>
          </div>
          <Button variant="primary">
            Ver Planos
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 text-blue-600 bg-white rounded-xl p-2 shadow-md">
              {ICONS.BRAIN}
            </div>
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Análise de Sentimento
              </h2>
              <p className="text-sm text-gray-600">Analise automaticamente o sentimento de textos e comentários</p>
            </div>
          </div>
          {usage && usage.limit > 0 && (
            <div className="bg-white/70 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <div className="text-sm text-gray-700 font-medium">
                {usage.used} / {usage.limit} análises este mês
              </div>
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Texto para análise
            </label>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Cole aqui o texto que deseja analisar (comentário, mensagem, demanda, etc.)"
              rows={4}
              className="w-full"
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          <Button
            onClick={handleAnalyze}
            disabled={!text.trim() || isAnalyzing}
            isLoading={isAnalyzing}
            loadingText="Analisando..."
            className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="w-5 h-5 mr-2">
              {ICONS.BRAIN}
            </div>
            Analisar Sentimento
          </Button>
        </div>
      </Card>

      {result && (
        <Card className="p-6 shadow-lg border-0 bg-white">
          <div className="flex items-center space-x-2 mb-6">
            <div className="w-6 h-6 text-green-600">
              {ICONS.CHECK}
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Resultado da Análise</h3>
          </div>
          
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Texto analisado:</h4>
              <p className="text-gray-900">{result.text}</p>
            </div>

            <div className={`border rounded-lg p-4 ${getSentimentColor(result.sentiment)}`}>
              <div className="flex items-center space-x-2 mb-2">
                {getSentimentIcon(result.sentiment)}
                <span className="font-semibold">
                  Sentimento: {getSentimentLabel(result.sentiment)}
                </span>
              </div>
              <p className="text-sm">
                Confiança: {(result.confidence * 100).toFixed(1)}%
              </p>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {(result.scores.positive * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Positivo</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {(result.scores.neutral * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Neutro</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {(result.scores.negative * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Negativo</div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Dica</h4>
              <p className="text-sm text-blue-700">
                Use esta análise para entender melhor o tom das mensagens dos cidadãos e 
                adaptar suas respostas de acordo com o sentimento identificado.
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default SentimentAnalysis;
