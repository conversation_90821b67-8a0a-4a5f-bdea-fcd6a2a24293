import React, { useState, useEffect } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { AISettings } from '../types';
import { getAISettings, saveAISettings } from '../services/firebaseService';
import { ICONS } from '../constants';

const AISettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<AISettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const aiSettings = await getAISettings();
      setSettings(aiSettings);
    } catch (err) {
      console.error('Erro ao carregar configurações:', err);
      setError('Erro ao carregar configurações de IA');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      await saveAISettings(settings);
      setSuccess('Configurações salvas com sucesso!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Erro ao salvar configurações:', err);
      setError('Erro ao salvar configurações');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (!settings) return;
    
    setSettings({
      ...settings,
      [field]: value,
    });
  };

  const handleNestedChange = (parent: string, field: string, value: any) => {
    if (!settings) return;
    
    setSettings({
      ...settings,
      [parent]: {
        ...settings[parent as keyof AISettings],
        [field]: value,
      },
    });
  };

  const testConnection = async () => {
    if (!settings?.geminiApiKey) {
      setError('Insira uma chave API válida primeiro');
      return;
    }

    setError(null);
    setSuccess(null);

    try {
      // Aqui você implementaria um teste real da API
      // Por enquanto, simular teste
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSuccess('✅ Conexão com Google Gemini estabelecida com sucesso!');
      setTimeout(() => setSuccess(null), 5000);
    } catch (err) {
      setError('❌ Falha ao conectar com a API. Verifique sua chave.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 mb-4">Erro ao carregar configurações</div>
        <Button onClick={loadSettings}>Tentar Novamente</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-neutral-light">
          🤖 Configurações de IA
        </h1>
        <Button
          onClick={handleSave}
          disabled={isSaving}
          isLoading={isSaving}
          loadingText="Salvando..."
          variant="primary"
        >
          💾 Salvar Configurações
        </Button>
      </div>

      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md">
          {error}
        </div>
      )}

      {success && (
        <div className="p-4 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-md">
          {success}
        </div>
      )}

      {/* Configuração da API */}
      <Card title="🔑 Configuração da API">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-2">
              Provedor de IA Preferido
            </label>
            <select
              value={settings.preferredProvider}
              onChange={(e) => handleInputChange('preferredProvider', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md 
                       focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light 
                       bg-white dark:bg-neutral-dark dark:text-neutral-light"
            >
              <option value="mock">🧪 Simulado (Gratuito)</option>
              <option value="gemini">🤖 Google Gemini</option>
              <option value="openai">🔮 OpenAI (Em breve)</option>
              <option value="anthropic">🧠 Anthropic (Em breve)</option>
            </select>
          </div>

          {settings.preferredProvider === 'gemini' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-2">
                Chave API do Google Gemini
              </label>
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <input
                    type={showApiKey ? 'text' : 'password'}
                    value={settings.geminiApiKey || ''}
                    onChange={(e) => handleInputChange('geminiApiKey', e.target.value)}
                    placeholder="AIzaSy... (Cole sua chave API aqui)"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md 
                             focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light 
                             bg-white dark:bg-neutral-dark dark:text-neutral-light pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <div className="w-4 h-4">
                      {showApiKey ? ICONS.EYE_OFF : ICONS.EYE}
                    </div>
                  </button>
                </div>
                <Button
                  onClick={testConnection}
                  variant="outline"
                  size="sm"
                  disabled={!settings.geminiApiKey}
                >
                  🧪 Testar
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-neutral-medium mt-1">
                Obtenha sua chave gratuita em: <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Google AI Studio</a>
              </p>
            </div>
          )}
        </div>
      </Card>

      {/* Funcionalidades Habilitadas */}
      <Card title="⚙️ Funcionalidades">
        <div className="space-y-3">
          {Object.entries(settings.enabledFeatures).map(([feature, enabled]) => (
            <label key={feature} className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-gray-700 dark:text-neutral-light">
                  {feature === 'sentimentAnalysis' && '😊 Análise de Sentimento'}
                  {feature === 'autoCategorization' && '🏷️ Categorização Automática'}
                  {feature === 'predictiveAnalytics' && '📈 Análise Preditiva'}
                  {feature === 'smartResponses' && '💬 Respostas Inteligentes'}
                </span>
                <p className="text-xs text-gray-500 dark:text-neutral-medium">
                  {feature === 'sentimentAnalysis' && 'Analisa o sentimento de textos e comentários'}
                  {feature === 'autoCategorization' && 'Categoriza demandas automaticamente'}
                  {feature === 'predictiveAnalytics' && 'Gera previsões baseadas em dados'}
                  {feature === 'smartResponses' && 'Sugere respostas automáticas'}
                </p>
              </div>
              <input
                type="checkbox"
                checked={enabled}
                onChange={(e) => handleNestedChange('enabledFeatures', feature, e.target.checked)}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
            </label>
          ))}
        </div>
      </Card>

      {/* Configurações Avançadas */}
      <Card title="🔧 Configurações Avançadas">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-2">
              Criatividade (Temperature)
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={settings.settings.temperature}
              onChange={(e) => handleNestedChange('settings', 'temperature', parseFloat(e.target.value))}
              className="w-full"
            />
            <div className="text-xs text-gray-500 text-center mt-1">
              {settings.settings.temperature} (0 = Conservador, 1 = Criativo)
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-2">
              Máximo de Tokens
            </label>
            <input
              type="number"
              min="100"
              max="4000"
              step="100"
              value={settings.settings.maxTokens}
              onChange={(e) => handleNestedChange('settings', 'maxTokens', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md 
                       focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light 
                       bg-white dark:bg-neutral-dark dark:text-neutral-light"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-2">
              Idioma
            </label>
            <select
              value={settings.settings.language}
              onChange={(e) => handleNestedChange('settings', 'language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md 
                       focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light 
                       bg-white dark:bg-neutral-dark dark:text-neutral-light"
            >
              <option value="pt-BR">🇧🇷 Português (Brasil)</option>
              <option value="en-US">🇺🇸 English (US)</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Estatísticas de Uso */}
      <Card title="📊 Uso da IA">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {settings.usage.monthlyRequests}
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300">
              Requisições este mês
            </div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {settings.preferredProvider === 'mock' ? 'Ilimitado' : 'Configurado'}
            </div>
            <div className="text-sm text-green-700 dark:text-green-300">
              Status da API
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AISettingsPage;
